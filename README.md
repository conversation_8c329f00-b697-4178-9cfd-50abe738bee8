# Telegram订单处理机器人

这是一个使用Go语言和Gin框架开发的Telegram机器人，用于自动处理商户订单查询和验证。

## 功能特性

### 核心功能
- **订单消息识别**: 自动识别商户群中的订单消息格式
- **Media Group支持**: 支持Telegram多图相册（Media Group）订单处理
- **多图片金额累加**: 自动扫描多张图片的二维码并累加金额验证
- **消息标记**: 对处理中的订单消息添加emoji反应
- **第三方API集成**: 查询订单信息和验证凭证
- **二维码扫描**: 从图片中提取二维码信息进行账户验证
- **多群组管理**: 支持商户群、供应商群和人工客服群的不同处理逻辑
- **并发处理**: 支持同时处理多个订单查询

### 订单处理流程

1. **订单接收**: 商户在群中发送订单消息（单图片或多图相册）
2. **消息标记**: 机器人立即标记该消息，表示正在处理
3. **信息提取**: 解析订单号、金额等关键信息
4. **API查询**: 调用第三方API查询订单状态
5. **图片处理**: 下载并扫描图片中的二维码（支持多图累加）
6. **信息验证**: 验证账户、金额等信息是否匹配
7. **状态处理**: 根据订单状态进行相应处理
8. **结果反馈**: 向商户群返回处理结果
9. **异常转发**: 失败时转发到客服群，自动标明来源群组

### 支持的订单格式

#### 1. 传统单图片订单格式
```
图片
Order Number: NO123456 (* Required)
Payer Name: MR.Demo (Optional)
Bank Account: *********** (Optional)
Date Time: 2024-07-18 12:32 (Optional)
Amount: 2000 (Optional)
```

#### 2. Media Group多图相册订单格式 ⭐ **新增**
```
多图相册（2-10张图片）
Caption: NO123456 (* Required, 只在最后一条消息中)
```

**Media Group特点**:
- 支持2张或更多图片的相册消息
- Caption（订单号）通常只出现在相册的最后一条消息中
- 机器人自动获取每张图片中最大尺寸的版本
- 扫描每张图片的二维码并累加金额
- 验证累加金额是否与订单查询结果匹配
- 支持账户不匹配时向商户群发送警告
- **智能消息转发**: 转发到客服群时自动标明来源群组信息
- **真实群组名称**: 通过Telegram API获取群组的真实名称

### 客服群消息格式

当消息转发到客服群时，会自动添加来源信息：

```
📍 来源: 商户群: ABC支付收款群          # 通过Telegram API获取的真实群组名称
🕐 时间: 2024-07-16 14:30:25           # 消息发送时间
📋 订单ID: ORDER123456                 # 提交订单ID
📸 多图相册: AgACAgIAAxkBAAIC          # Media Group ID (仅多图消息)
━━━━━━━━━━━━━━━━━━━━
原始消息内容...

❌ 处理失败: 具体失败原因
💡 提示: 这是多图相册中的一张图片 (仅多图消息)
```

### 群组名称获取机制

1. **优先使用Telegram API**: 通过`getChat`接口获取群组真实名称
2. **缓存机制**: 避免重复API调用，提高性能
3. **回退机制**: API失败时使用配置文件中的名称
4. **错误处理**: 处理权限不足、群组不存在等异常情况

## 配置说明

### 环境变量

复制 `.env.example` 为 `.env` 并配置以下变量：

```bash
# 服务器配置
PORT=8080

# Telegram Bot配置
TELEGRAM_BOT_TOKEN=your_bot_token_here

# 第三方API配置
THIRD_PARTY_API_BASE_URL=https://aut.mb.rushingpay.com
THIRD_PARTY_API_QUERY_PATH=/ordercheck/query
THIRD_API_KEY=your_api_key_here

# 二维码API配置
QRCODE_API_ACCOUNT_NO=your_account_no_here
QRCODE_API_BASE_URL=https://api-fin.uexchange.io/v1/fin/bank/
QRCODE_API_LICENSE_KEY=your_license_key_here
QRCODE_API_ACCESS_KEY=your_access_key_here

# 群组配置
# 人工客服群ID
CUSTOMER_SERVICE_GROUP=-*************

# 供应商群组ID配置
SUPPLIER_GROUP_TOPPAY=-*************
SUPPLIER_GROUP_VADERPAY=-*************
SUPPLIER_GROUP_THPAY=-*************
SUPPLIER_GROUP_EXKUB=-*************

# 日志配置（可选）
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=100
LOG_MAX_BACKUPS=3
LOG_MAX_AGE=28
LOG_COMPRESS=true
LOG_CONSOLE=true
```

### 供应商群组映射

| 支付机构 | 对应群组名称 | 环境变量 |
|---------|-------------|----------|
| TAIP496-Cloud168-TOPPAY | TopPayCloud168-Cloud168 | SUPPLIER_GROUP_TOPPAY |
| VaderPay-superkapoo888 | VDP CS - datodes / superkapoo888 | SUPPLIER_GROUP_VADERPAY |
| THPay-THPayCloudpay888 | M124-Cloudpay888-THPAY1 % | SUPPLIER_GROUP_THPAY |
| ExkubScbRs-exkubscbIOne | Cloudpay Order Checking | SUPPLIER_GROUP_EXKUB |

## 安装和运行

### 本地开发

1. 克隆项目
```bash
git clone <repository-url>
cd telegram-order-bot
```

2. 安装依赖
```bash
go mod tidy
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，填入实际配置
```

4. 运行项目
```bash
go run main.go
```

### Docker部署

1. 构建镜像
```bash
docker build -t telegram-order-bot .
```

2. 使用docker-compose运行
```bash
docker-compose up -d
```

## API端点

- `GET /health` - 健康检查
- `POST /webhook/{bot_token}` - Telegram webhook端点

## 设置Webhook

运行服务后，需要设置Telegram webhook：

```bash
curl -X POST "https://api.telegram.org/bot{BOT_TOKEN}/setWebhook" \
     -H "Content-Type: application/json" \
     -d '{"url": "https://your-domain.com/webhook/{BOT_TOKEN}"}'
```

## 项目结构

```
.
├── main.go                 # 主程序入口
├── internal/
│   ├── config/            # 配置管理
│   ├── handlers/          # HTTP处理器
│   ├── models/            # 数据模型
│   └── services/          # 业务逻辑服务
├── Dockerfile             # Docker构建文件
├── docker-compose.yml     # Docker Compose配置
└── README.md             # 项目说明
```

## 注意事项

1. **安全性**: 确保bot token和API密钥的安全
2. **权限**: 机器人需要在相关群组中有发送消息和添加反应的权限
3. **网络**: 确保服务器可以访问Telegram API和第三方API
4. **日志**: 查看日志文件了解运行状态和错误信息

## 故障排除

### 常见问题

1. **Webhook设置失败**: 检查域名和SSL证书
2. **消息无响应**: 检查群组ID配置和机器人权限
3. **API调用失败**: 检查第三方API配置和网络连接
4. **二维码识别失败**: 确保图片清晰且包含有效二维码

### 日志查看

```bash
# Docker环境
docker-compose logs -f telegram-bot

# 本地环境
tail -f logs/app.log
```
