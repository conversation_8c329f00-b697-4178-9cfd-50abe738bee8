# 版本信息

## 当前版本: v2.2.0

### 发布日期: 2025-07-17

### 🎉 重大新功能

#### 📸 Media Group多图相册支持
- **Telegram相册处理**: 完整支持Telegram的Media Group机制
- **智能消息收集**: 自动收集同一相册中的所有消息
- **延迟处理机制**: 2秒延迟确保收集完整相册
- **多图二维码扫描**: 逐个扫描每张图片的二维码
- **精确金额累加**: 使用decimal类型累加所有图片金额
- **金额验证**: 支持精确匹配、向上取整、向下取整三种比较方式

#### 🏷️ Telegram API群组名称获取
- **真实群组名称**: 通过Telegram `getChat` API获取群组真实名称
- **智能缓存机制**: 避免重复API调用，提高性能
- **完善错误处理**: 处理权限不足、群组不存在等异常情况
- **回退机制**: API失败时自动使用配置文件名称
- **增强转发格式**: 转发消息包含真实群组名称和时间戳

### 🔧 技术改进

#### Media Group处理流程
1. **消息识别**: 检测`media_group_id`并缓存相关消息
2. **延迟收集**: 等待2秒收集完整相册
3. **订单解析**: 检查最后的caption是否为订单号
4. **图片处理**: 获取每张图片的最大尺寸版本
5. **二维码扫描**: 逐个扫描并验证二维码
6. **金额累加**: 使用decimal精确累加所有金额
7. **结果验证**: 与订单查询金额进行比较

#### 群组名称获取机制
1. **缓存查找**: 优先从内存缓存获取
2. **API调用**: 调用Telegram `getChat` API
3. **类型识别**: 自动识别商户群、供应商群、客服群
4. **名称构建**: 组合群组类型和真实名称
5. **缓存存储**: 存储结果避免重复调用

### 📊 支持的订单格式

#### 传统格式（保持不变）
1. **Order Number: 订单号** (图片+文本)
2. **Merchant Order No. 订单号** (图片+文本)
3. **Merchant: 订单号** (图片+文本)
4. **订单号** (图片+单行文本)

#### 新增格式 ⭐
5. **Media Group相册** (多图片+单行订单号)
   - 支持2张或更多图片的相册消息
   - Caption（订单号）只在最后一条消息中
   - 自动获取每张图片的最大尺寸版本
   - 扫描所有图片二维码并累加金额

### 🎯 转发消息格式升级

#### 新的转发格式
```
📍 来源: 商户群: ABC支付收款群          # 真实群组名称
🕐 时间: 2024-07-17 01:15:30           # 消息时间戳
📋 订单ID: ORDER123456                 # 提交订单ID
📸 多图相册: AgACAgIAAxkBAAIC          # Media Group ID (仅多图)
━━━━━━━━━━━━━━━━━━━━
原始消息内容...

❌ 处理失败: 具体失败原因
💡 提示: 这是多图相册中的一张图片 (仅多图)
```

### 🔒 安全和性能优化

#### 安全改进
- 静态编译避免依赖问题
- systemd安全配置增强
- 用户权限最小化原则
- 资源限制和保护机制

#### 性能优化
- 群组名称缓存机制
- 并发处理多个订单
- 高效的图片下载和处理
- 智能的错误恢复机制

### 📦 部署包内容

```
telegram-order-bot-v2.2.0-linux-package/
├── telegram-order-bot-linux-amd64    # x86_64架构二进制文件
├── telegram-order-bot-linux-arm64    # aarch64架构二进制文件
├── start.sh                          # 智能启动脚本 v2.2.0
├── install.sh                        # 多架构安装脚本 v2.2.0
├── .env.example                      # 配置文件模板
├── get-group-id.sh                   # 群组ID获取工具
├── README.md                         # 项目说明
├── DEPLOYMENT.md                     # 部署文档
└── VERSION.md                        # 本文件
```

### 🚀 使用方法

#### 自动安装 (推荐)
```bash
sudo ./install.sh
```

#### 手动启动
```bash
./start.sh
```

### 🧪 测试覆盖

- ✅ Media Group消息识别测试
- ✅ 多图片订单解析测试
- ✅ Decimal金额累加测试
- ✅ Telegram API群组名称获取测试
- ✅ 错误处理和回退机制测试
- ✅ 缓存机制测试

### 🔄 兼容性

- ✅ 完全向后兼容现有4种订单格式
- ✅ 不影响现有配置和API接口
- ✅ 平滑升级，无需额外配置
- ✅ 支持x86_64和aarch64架构

### 📈 典型使用场景

#### 场景1: 多图相册订单
1. 商户选择3张付款截图创建相册
2. 在最后一张图片添加订单号
3. 发送到商户群
4. 机器人自动处理并累加金额

#### 场景2: 群组名称显示
1. 群组改名为"ABC公司正式收款群"
2. 转发消息自动显示真实群组名称
3. 客服立即识别消息来源

### 🎉 升级亮点

- 🆕 **Media Group支持**: 处理多图相册订单
- 🏷️ **真实群组名称**: 显示Telegram群组真实名称
- 💰 **精确金额计算**: Decimal类型金融级精度
- 🔧 **智能错误处理**: 完善的异常处理机制
- 📊 **增强日志**: 详细的处理过程记录
- 🚀 **性能优化**: 缓存和并发处理优化

---

## 历史版本

### v2.1.1 (2025-07-16)
- 修复部署问题
- 多架构支持
- 静态编译

### v2.1.0 (2025-07-16)
- 第五种多图片消息格式支持
- Decimal精确金额计算

### v2.0.0 (2025-07-15)
- 基础订单处理功能
- 4种订单消息格式支持

### v1.0.0 (2025-07-14)
- 初始版本
