[Unit]
Description=Telegram Order Bot Service v2.1.0
Documentation=file:///opt/telegram-order-bot/README.md
After=network-online.target
Wants=network-online.target
StartLimitIntervalSec=60
StartLimitBurst=3

[Service]
Type=simple
User=telegram-bot
Group=telegram-bot
WorkingDirectory=/opt/telegram-order-bot
ExecStart=/opt/telegram-order-bot/telegram-order-bot
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
TimeoutStartSec=30
TimeoutStopSec=30

# 输出和日志
StandardOutput=journal
StandardError=journal
SyslogIdentifier=telegram-order-bot

# 环境变量
Environment=GIN_MODE=release
Environment=LC_ALL=C
Environment=LANG=C

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/telegram-order-bot/logs
PrivateTmp=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
LockPersonality=true
MemoryDenyWriteExecute=true
RestrictNamespaces=true
SystemCallFilter=@system-service
SystemCallErrorNumber=EPERM

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
