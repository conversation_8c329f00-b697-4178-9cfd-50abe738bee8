#!/bin/bash

# Telegram Order Bot 启动脚本
# 自动检测系统架构并选择合适的二进制文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 修复locale警告
fix_locale() {
    if [[ -z "$LC_ALL" ]] || [[ "$LC_ALL" == "en_US.UTF-8" ]]; then
        export LC_ALL=C
        export LANG=C
        log_debug "设置locale为C以避免警告"
    fi
}

# 检测系统架构
detect_architecture() {
    local arch=$(uname -m)
    local binary_name=""
    
    case $arch in
        x86_64|amd64)
            binary_name="telegram-order-bot-amd64"
            log_info "检测到架构: x86_64/amd64"
            ;;
        aarch64|arm64)
            binary_name="telegram-order-bot-arm64"
            log_info "检测到架构: aarch64/arm64"
            ;;
        armv7l|armv6l)
            log_error "暂不支持32位ARM架构: $arch"
            log_info "请使用64位系统或联系支持"
            exit 1
            ;;
        *)
            log_error "不支持的架构: $arch"
            log_info "支持的架构: x86_64, aarch64"
            exit 1
            ;;
    esac
    
    echo "$binary_name"
}

# 检查二进制文件
check_binary() {
    local binary_name="$1"
    
    if [[ ! -f "./$binary_name" ]]; then
        log_error "找不到二进制文件: $binary_name"
        log_info "可用的二进制文件:"
        ls -la telegram-order-bot-* 2>/dev/null || log_error "没有找到任何二进制文件"
        exit 1
    fi
    
    if [[ ! -x "./$binary_name" ]]; then
        log_info "设置可执行权限: $binary_name"
        chmod +x "./$binary_name"
    fi
    
    # 测试二进制文件是否可执行
    if ! "./$binary_name" --help >/dev/null 2>&1; then
        log_error "二进制文件无法执行: $binary_name"
        log_info "可能的原因:"
        log_info "1. 架构不匹配"
        log_info "2. 缺少必要的库"
        log_info "3. 权限问题"
        
        # 显示文件信息
        if command -v file >/dev/null 2>&1; then
            log_info "文件信息:"
            file "./$binary_name"
        fi
        
        exit 1
    fi
    
    log_info "二进制文件检查通过: $binary_name"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_warn "检测到以root用户运行，建议使用普通用户运行此程序"
        read -p "是否继续? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 检查必要文件
check_files() {
    log_info "检查必要文件..."
    
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.example" ]]; then
            log_warn "未找到 .env 文件，将从 .env.example 复制"
            cp .env.example .env
            log_error "请编辑 .env 文件配置必要的参数后重新运行"
            exit 1
        else
            log_error "未找到 .env 或 .env.example 文件"
            exit 1
        fi
    fi
}

# 检查配置
check_config() {
    log_info "检查配置文件..."
    
    # 检查必要的环境变量
    source .env
    
    local missing_vars=()
    
    if [[ -z "$TELEGRAM_BOT_TOKEN" || "$TELEGRAM_BOT_TOKEN" == "your_bot_token_here" ]]; then
        missing_vars+=("TELEGRAM_BOT_TOKEN")
    fi
    
    if [[ -z "$THIRD_API_KEY" || "$THIRD_API_KEY" == "your_api_key_here" ]]; then
        missing_vars+=("THIRD_API_KEY")
    fi
    
    if [[ -z "$QRCODE_API_ACCOUNT_NO" || "$QRCODE_API_ACCOUNT_NO" == "your_account_no_here" ]]; then
        missing_vars+=("QRCODE_API_ACCOUNT_NO")
    fi
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "以下配置项需要设置:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        log_error "请编辑 .env 文件设置这些配置项"
        exit 1
    fi
    
    log_info "配置检查通过"
}

# 创建日志目录
create_log_dir() {
    if [[ ! -d "logs" ]]; then
        log_info "创建日志目录..."
        mkdir -p logs
    fi
}

# 显示系统信息
show_system_info() {
    log_info "系统信息:"
    log_info "  操作系统: $(uname -s)"
    log_info "  架构: $(uname -m)"
    log_info "  内核版本: $(uname -r)"
    if command -v lsb_release >/dev/null 2>&1; then
        log_info "  发行版: $(lsb_release -d | cut -f2)"
    fi
}

# 显示启动信息
show_startup_info() {
    echo
    log_info "=========================================="
    log_info "  Telegram Order Bot 启动中..."
    log_info "=========================================="
    log_info "版本: v2.1.0 (支持第五种多图片消息格式)"
    log_info "功能: 订单处理、二维码验证、多图片金额累加"
    log_info "二进制: $1"
    log_info "日志: ./logs/app.log"
    log_info "配置: .env"
    echo
    log_info "按 Ctrl+C 停止程序"
    log_info "=========================================="
    echo
}

# 主函数
main() {
    log_info "Telegram Order Bot 启动脚本 v2.1.0"
    echo
    
    # 修复locale
    fix_locale
    
    # 显示系统信息
    show_system_info
    echo
    
    check_root
    
    # 检测架构并选择二进制文件
    local binary_name=$(detect_architecture)
    
    check_binary "$binary_name"
    check_files
    check_config
    create_log_dir
    show_startup_info "$binary_name"
    
    # 启动程序
    log_info "启动 Telegram Order Bot..."
    exec "./$binary_name"
}

# 信号处理
trap 'log_info "收到停止信号，正在关闭..."; exit 0' SIGINT SIGTERM

# 运行主函数
main "$@"
