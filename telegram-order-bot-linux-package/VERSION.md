# 版本信息

## 当前版本: v2.2.1

### 发布日期: 2025-01-17

### 🎉 新功能

#### 📸 多图相册批量转发
- **sendMediaGroup支持**: 失败的多图相册订单现在会将所有图片一起转发到客服群
- **智能媒体组构建**: 自动选择最大尺寸图片，第一张图片包含完整说明信息
- **回退机制**: 如果批量发送失败，自动切换到逐张发送模式
- **增强日志**: 详细记录转发过程和图片数量

#### 🔍 订单查询日志增强
- **详细请求日志**: 记录完整的API请求数据和URL
- **原始响应日志**: 记录API返回的原始响应内容
- **结构化订单信息**: 详细记录所有订单字段信息
- **错误追踪**: 完善的错误日志记录

#### 🚀 多图相册处理优化
- **去重机制**: 解决多图相册重复处理问题
- **智能延迟**: 动态检测消息收集完成时机
- **状态跟踪**: 防止重复的引用回复和转发
- **内存管理**: 定期清理过期缓存

### 🔧 技术改进
- **多架构支持**: 提供x86_64和aarch64两种架构的二进制文件
- **静态链接**: 使用CGO_ENABLED=0编译，避免动态库依赖问题
- **Locale修复**: 修复LC_ALL locale警告问题
- **智能架构检测**: 启动脚本自动检测系统架构并选择合适的二进制文件

#### 📦 部署包改进
- **多二进制文件**: 包含telegram-order-bot-amd64和telegram-order-bot-arm64
- **智能启动脚本**: 自动检测架构，修复locale问题
- **增强安装脚本**: 支持多架构自动选择和安装
- **改进的systemd服务**: 添加更多安全设置和资源限制

#### 🛠️ 技术改进
- **静态编译**: 避免"cannot execute binary file"错误
- **错误诊断**: 启动脚本提供详细的错误诊断信息
- **系统兼容性**: 支持更多Linux发行版和架构

### 支持的架构

- ✅ **x86_64 (amd64)**: Intel/AMD 64位处理器
- ✅ **aarch64 (arm64)**: ARM 64位处理器 (如树莓派4、Apple M1等)
- ❌ **armv7l/armv6l**: 32位ARM (暂不支持)

### 部署包内容

```
telegram-order-bot-linux-package/
├── telegram-order-bot-amd64       # x86_64架构二进制文件
├── telegram-order-bot-arm64       # aarch64架构二进制文件
├── start.sh                       # 智能启动脚本 (自动检测架构)
├── install.sh                     # 多架构安装脚本
├── telegram-order-bot.service     # 改进的systemd服务文件
├── .env.example                   # 配置文件模板
├── get-group-id.sh               # 群组ID获取工具
├── README.md                      # 项目说明
├── DEPLOYMENT.md                  # 部署文档
└── VERSION.md                     # 本文件
```

### 使用方法

#### 自动安装 (推荐)
```bash
sudo ./install.sh
```
安装脚本会自动检测系统架构并选择合适的二进制文件。

#### 手动启动
```bash
./start.sh
```
启动脚本会自动检测架构并启动对应的二进制文件。

### 错误修复

#### 问题1: "cannot execute binary file"
**原因**: 架构不匹配或缺少动态库
**解决**: 使用静态编译和多架构支持

#### 问题2: "setlocale: LC_ALL: cannot change locale"
**原因**: 系统locale设置问题
**解决**: 脚本中设置LC_ALL=C

#### 问题3: 权限问题
**原因**: 二进制文件没有执行权限
**解决**: 脚本自动设置可执行权限

### 兼容性测试

已在以下系统测试通过:
- Ubuntu 20.04/22.04 (x86_64)
- CentOS 8/9 (x86_64)
- Debian 11/12 (x86_64)
- Raspberry Pi OS (aarch64)
- Amazon Linux 2 (x86_64)

### 功能特性 (保持不变)

- ✅ 支持5种订单消息格式
- ✅ 多图片二维码金额累加验证
- ✅ 精确的decimal金额计算
- ✅ 智能账户验证和错误处理
- ✅ 多群组管理和消息转发

---

## 历史版本

### v2.1.0 (2025-07-16)
- 第五种多图片消息格式支持
- Decimal精确金额计算
- 增强账户验证

### v2.0.0 (2025-07-15)
- 基础订单处理功能
- 4种订单消息格式支持

### v1.0.0 (2025-07-14)
- 初始版本
