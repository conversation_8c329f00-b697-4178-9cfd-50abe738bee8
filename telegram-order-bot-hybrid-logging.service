[Unit]
Description=Telegram Order Bot Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=telegram-bot
Group=telegram-bot
WorkingDirectory=/www/robot
ExecStart=/www/robot/telegram-order-bot-linux
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5

# 混合日志配置 - 便于调试
StandardOutput=journal
StandardError=journal
SyslogIdentifier=telegram-order-bot

# 环境配置
EnvironmentFile=-/www/robot/.env
Environment="LOG_CONSOLE=true"
Environment="LOG_LEVEL=debug"

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/www/robot/logs

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
