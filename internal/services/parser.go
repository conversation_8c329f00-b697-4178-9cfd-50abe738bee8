package services

import (
	"regexp"
	"strings"
	"telegram-order-bot/internal/logger"
	"telegram-order-bot/internal/models"
)

// MessageParser message parser
type MessageParser struct{}

// NewMessageParser creates a new message parser
func NewMessageParser() *MessageParser {
	return &MessageParser{}
}

// getMessageContent gets message content (only processes caption field of image messages)
func (p *MessageParser) getMessageContent(message *models.Message) string {
	// Only process image messages, not pure text messages
	if len(message.Photo) > 0 {
		logger.WithFields(map[string]interface{}{
			"photo_count": len(message.Photo),
			"caption":     message.Caption,
		}).Debug("Processing image message, using caption field")
		return message.Caption
	}
	// Pure text messages are not processed
	logger.Debug("Pure text message, ignoring")
	return ""
}

// getSupplierMessageContent gets supplier message content (processes text field of pure text messages)
func (p *MessageParser) getSupplierMessageContent(message *models.Message) string {
	// Supplier replies are usually pure text messages
	if message.Text != "" {
		logger.WithFields(map[string]interface{}{
			"text": message.Text,
		}).Debug("Processing supplier text message, using text field")
		return message.Text
	}
	// If there are images, also try to get caption
	if len(message.Photo) > 0 && message.Caption != "" {
		logger.WithFields(map[string]interface{}{
			"photo_count": len(message.Photo),
			"caption":     message.Caption,
		}).Debug("Processing supplier image message, using caption field")
		return message.Caption
	}
	logger.Debug("Empty supplier message content")
	return ""
}

// IsOrderMessage determines if it's an order message
func (p *MessageParser) IsOrderMessage(message *models.Message) bool {
	content := p.getMessageContent(message)
	if content == "" {
		return false
	}

	// Check four order message formats (image messages)
	patterns := []string{
		`(?i)order\s+number\s*:\s*([A-Za-z0-9]+)`,      // 1. Order Number: merchant order ID
		`(?i)merchant\s+order\s+no\.\s*([A-Za-z0-9]+)`, // 2. Merchant Order No. merchant order ID
		`(?i)merchant\s*:\s*([A-Za-z0-9]+)`,            // 3. Merchant: merchant order ID
		`^([A-Za-z0-9]+)$`,                             // 4. merchant order ID (image with only one line of merchant order ID)
	}

	// Clean content (remove leading and trailing spaces and newlines)
	cleanContent := strings.TrimSpace(content)

	for _, pattern := range patterns {
		if matched, _ := regexp.MatchString(pattern, cleanContent); matched {
			return true
		}
	}

	return false
}

// IsMediaGroupOrderMessage determines if Media Group caption is an order message
func (p *MessageParser) IsMediaGroupOrderMessage(caption string) bool {
	if caption == "" {
		return false
	}

	// Media Group order message format: only supports single line order ID
	cleanContent := strings.TrimSpace(caption)
	singleOrderPattern := `^([A-Za-z0-9]+)$`
	matched, _ := regexp.MatchString(singleOrderPattern, cleanContent)

	logger.WithFields(map[string]interface{}{
		"caption":              caption,
		"clean_content":        cleanContent,
		"is_media_group_order": matched,
	}).Debug("Checking media group order message")

	return matched
}

// ParseMediaGroupOrderInfo 解析Media Group订单信息
func (p *MessageParser) ParseMediaGroupOrderInfo(messages []*MediaGroupMessage, caption string) *models.OrderInfo {
	if !p.IsMediaGroupOrderMessage(caption) {
		return nil
	}

	orderInfo := &models.OrderInfo{
		IsMediaGroup: true,
		MediaGroupID: "", // 将在调用处设置
	}

	// 提取订单号
	cleanContent := strings.TrimSpace(caption)
	orderInfo.OrderNumber = cleanContent

	// 收集所有图片的最大尺寸文件ID
	var imageFileIDs []string
	for _, msgWrapper := range messages {
		message := msgWrapper.Message
		if len(message.Photo) > 0 {
			// 选择最大尺寸的图片
			largestPhoto := message.Photo[0]
			for _, photo := range message.Photo {
				if photo.FileSize > largestPhoto.FileSize {
					largestPhoto = photo
				}
			}
			imageFileIDs = append(imageFileIDs, largestPhoto.FileID)

			// 设置MediaGroupID
			if orderInfo.MediaGroupID == "" {
				orderInfo.MediaGroupID = message.MediaGroupID
			}
		}
	}

	if len(imageFileIDs) > 0 {
		orderInfo.HasImage = true
		orderInfo.ImageFileIDs = imageFileIDs
		// 为了兼容性，设置第一个图片ID
		orderInfo.ImageFileID = imageFileIDs[0]
	}

	logger.WithFields(map[string]interface{}{
		"order_number":   orderInfo.OrderNumber,
		"media_group_id": orderInfo.MediaGroupID,
		"image_count":    len(imageFileIDs),
		"image_file_ids": imageFileIDs,
	}).Info("Parsed media group order info")

	return orderInfo
}

// IsAccountInquiry determines if it's an account inquiry message
func (p *MessageParser) IsAccountInquiry(message *models.Message) bool {
	// Must be an image message
	if len(message.Photo) == 0 {
		return false
	}

	caption := strings.ToLower(strings.TrimSpace(message.Caption))
	if caption == "" {
		return false
	}

	// Check if it contains account inquiry keywords
	inquiryPatterns := []string{
		`(?i)is\s+this\s+our\s+account`, // Is this our account
		`(?i)ใช่บัญชีของทางเราไหมครับ`, // Thai: Is this our account?
		`(?i)ใช่บัญชีของเราไหม`,        // Thai simplified version
		`(?i)是不是我们的账户`,                 // Chinese: Is this our account?
		`(?i)这是我们的账户吗`,                 // Chinese variant
	}

	for _, pattern := range inquiryPatterns {
		if matched, _ := regexp.MatchString(pattern, caption); matched {
			logger.WithFields(map[string]interface{}{
				"caption": caption,
				"pattern": pattern,
			}).Debug("Account inquiry message detected")
			return true
		}
	}

	return false
}

// ParseOrderInfo parses order information
func (p *MessageParser) ParseOrderInfo(message *models.Message) *models.OrderInfo {
	if !p.IsOrderMessage(message) {
		return nil
	}

	orderInfo := &models.OrderInfo{}
	content := p.getMessageContent(message)

	// Parse four order ID formats (image messages)
	patterns := []string{
		`(?i)order\s+number\s*:\s*([A-Za-z0-9]+)`,      // 1. Order Number: merchant order ID
		`(?i)merchant\s+order\s+no\.\s*([A-Za-z0-9]+)`, // 2. Merchant Order No. merchant order ID
		`(?i)merchant\s*:\s*([A-Za-z0-9]+)`,            // 3. Merchant: merchant order ID
		`^([A-Za-z0-9]+)$`,                             // 4. merchant order ID (image with only one line of merchant order ID)
	}

	// Clean content (remove leading and trailing spaces and newlines)
	cleanContent := strings.TrimSpace(content)

	for _, pattern := range patterns {
		if matches := regexp.MustCompile(pattern).FindStringSubmatch(cleanContent); len(matches) > 1 {
			orderInfo.OrderNumber = strings.TrimSpace(matches[1])
			break
		}
	}

	// Check if there are images
	if len(message.Photo) > 0 {
		orderInfo.HasImage = true
		// Select the largest image
		largestPhoto := message.Photo[0]
		for _, photo := range message.Photo {
			if photo.FileSize > largestPhoto.FileSize {
				largestPhoto = photo
			}
		}
		orderInfo.ImageFileID = largestPhoto.FileID
	}

	return orderInfo
}

// IsSupplierReply determines if it's a supplier reply
func (p *MessageParser) IsSupplierReply(message *models.Message) bool {
	if message.ReplyToMessage == nil {
		return false
	}

	// Check if the replied message contains submit order ID (supplier reply original message may be image or pure text)
	replyToContent := p.getSupplierMessageContent(message.ReplyToMessage)
	if replyToContent == "" {
		// If supplier method can't get content, try merchant method (compatibility)
		replyToContent = p.getMessageContent(message.ReplyToMessage)
	}

	submitOrderPattern := `(?i)submit.*order.*id|order.*id.*submit`
	matched, _ := regexp.MatchString(submitOrderPattern, replyToContent)

	return matched
}

// ParseSupplierReply 解析供应商回复
func (p *MessageParser) ParseSupplierReply(message *models.Message) *SupplierReply {
	if !p.IsSupplierReply(message) {
		return nil
	}

	reply := &SupplierReply{
		OriginalMessage: message.ReplyToMessage,
		ReplyMessage:    message,
	}

	// 从原消息中提取提交订单号（供应商回复的原消息可能是图文或纯文本）
	replyToContent := p.getSupplierMessageContent(message.ReplyToMessage)
	if replyToContent == "" {
		// 如果供应商方法获取不到内容，尝试用商户方法（兼容性）
		replyToContent = p.getMessageContent(message.ReplyToMessage)
	}

	submitOrderPattern := `(?i)submit.*order.*id\s*:\s*([A-Za-z0-9]+)|order.*id\s*:\s*([A-Za-z0-9]+)`
	if matches := regexp.MustCompile(submitOrderPattern).FindStringSubmatch(replyToContent); len(matches) > 1 {
		for i := 1; i < len(matches); i++ {
			if matches[i] != "" {
				reply.SubmitOrderID = strings.TrimSpace(matches[i])
				break
			}
		}
	}

	// Check supplier reply content (supplier replies are usually pure text)
	replyContent := p.getSupplierMessageContent(message)

	// Supplier reply supports four formats
	successPatterns := []string{
		// 1. Submit order ID + success|successful
		`(?i)([A-Za-z0-9]+)\s*(success|successful)`,
		// 2. Submit order ID + Already notified and success|successful
		`(?i)([A-Za-z0-9]+)\s*already\s+notified\s+and\s+(success|successful)`,
		// 3. Submit order ID + Chinese "成功" (success)
		`(?i)([A-Za-z0-9]+)\s*成功`,
		// 4. Vaderpay did received this fund under + order ID + optional extra line
		`(?i)vaderpay\s+did\s+received\s+this\s+fund\s+under\s+([A-Za-z0-9]+)(?:\s+[A-Za-z0-9_-]+)?`,
	}

	var orderFromReply string
	var statusFromReply string
	var patternMatched bool

	for i, pattern := range successPatterns {
		if matches := regexp.MustCompile(pattern).FindStringSubmatch(replyContent); len(matches) > 1 {
			orderFromReply = strings.TrimSpace(matches[1])
			if len(matches) > 2 && matches[2] != "" {
				statusFromReply = strings.TrimSpace(matches[2])
			} else if i == 2 { // Chinese "成功" (success) format
				statusFromReply = "成功"
			} else if i == 3 { // Vaderpay format
				statusFromReply = "received"
			}
			patternMatched = true

			logger.WithFields(map[string]interface{}{
				"pattern_index":     i + 1,
				"order_from_reply":  orderFromReply,
				"status_from_reply": statusFromReply,
				"submit_order_id":   reply.SubmitOrderID,
			}).Debug("Found order and status in supplier reply")
			break
		}
	}

	if patternMatched {
		// 验证回复中的订单号是否与提交订单号匹配
		if reply.SubmitOrderID != "" && orderFromReply == reply.SubmitOrderID {
			reply.IsSuccess = true
			reply.Status = "success"
			reply.ReplyContent = replyContent
		} else {
			// 订单号不匹配，记录警告
			logger.WithFields(map[string]interface{}{
				"expected_order_id": reply.SubmitOrderID,
				"reply_order_id":    orderFromReply,
			}).Warn("Order ID mismatch in supplier reply")
			reply.IsSuccess = false
			reply.Status = "order_mismatch"
			reply.ReplyContent = replyContent
		}
	} else {
		// 没有找到完整的订单号+状态格式
		reply.IsSuccess = false
		reply.Status = "invalid_format"
		reply.ReplyContent = replyContent
	}

	return reply
}

// SupplierReply 供应商回复结构
type SupplierReply struct {
	OriginalMessage *models.Message `json:"original_message"`
	ReplyMessage    *models.Message `json:"reply_message"`
	SubmitOrderID   string          `json:"submit_order_id"`
	IsSuccess       bool            `json:"is_success"`
	Status          string          `json:"status"`
	ReplyContent    string          `json:"reply_content"`
}
