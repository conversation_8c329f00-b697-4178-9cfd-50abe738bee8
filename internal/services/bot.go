package services

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"telegram-order-bot/internal/config"
	"telegram-order-bot/internal/logger"
	"telegram-order-bot/internal/models"
	"time"

	"github.com/shopspring/decimal"
)

// MediaGroupMessage single message in Media Group
type MediaGroupMessage struct {
	Message   *models.Message
	Timestamp time.Time
}

// MediaGroupCache Media Group cache
type MediaGroupCache struct {
	Messages    []*MediaGroupMessage
	LastCaption string
	LastUpdate  time.Time
	Processing  bool      // mark if processing
	Processed   bool      // mark if processing completed
	ProcessedAt time.Time // processing completion time
}

// BotService bot core service
type BotService struct {
	telegramService *TelegramService
	orderService    *OrderService
	qrService       *QRCodeService
	groupService    *GroupService
	parser          *MessageParser
	config          *config.Config

	// order processing state management
	processingOrders sync.Map // map[string]*models.OrderProcessingState

	// group type cache
	groupTypes sync.Map // map[int64]models.GroupType

	// group name cache
	groupNames sync.Map // map[int64]string

	// Media Group cache management
	mediaGroups sync.Map // map[string]*MediaGroupCache
}

// NewBotService creates a new bot service
func NewBotService(telegramService *TelegramService, orderService *OrderService, qrService *QRCodeService, groupService *GroupService, cfg *config.Config) *BotService {
	service := &BotService{
		telegramService: telegramService,
		orderService:    orderService,
		qrService:       qrService,
		groupService:    groupService,
		parser:          NewMessageParser(),
		config:          cfg,
	}

	// 设置群组删除时的缓存清除回调
	if groupService != nil {
		groupService.SetCacheCleanupCallback(service.clearGroupCache)
	}

	// start periodic cleanup of processed media group cache
	go service.startMediaGroupCacheCleanup()

	return service
}

// clearGroupCache 清除指定群组的缓存
func (b *BotService) clearGroupCache(telegramGroupID string) {
	// 将字符串ID转换为int64
	chatID, err := strconv.ParseInt(telegramGroupID, 10, 64)
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"telegram_group_id": telegramGroupID,
			"error":             err,
		}).Error("Failed to parse telegram group ID for cache cleanup")
		return
	}

	// 清除群组类型缓存
	if _, loaded := b.groupTypes.LoadAndDelete(chatID); loaded {
		logger.WithFields(map[string]interface{}{
			"chat_id":           chatID,
			"telegram_group_id": telegramGroupID,
		}).Info("Cleared group type cache")
	}

	// 清除群组名称缓存
	if _, loaded := b.groupNames.LoadAndDelete(chatID); loaded {
		logger.WithFields(map[string]interface{}{
			"chat_id":           chatID,
			"telegram_group_id": telegramGroupID,
		}).Info("Cleared group name cache")
	}

	logger.WithFields(map[string]interface{}{
		"chat_id":           chatID,
		"telegram_group_id": telegramGroupID,
	}).Info("Group cache cleanup completed")
}

// startMediaGroupCacheCleanup 启动定期清理已处理的media group缓存
func (b *BotService) startMediaGroupCacheCleanup() {
	ticker := time.NewTicker(10 * time.Minute) // 每10分钟清理一次
	defer ticker.Stop()

	for range ticker.C {
		b.cleanupProcessedMediaGroups()
	}
}

// cleanupProcessedMediaGroups 清理已处理的media group缓存
func (b *BotService) cleanupProcessedMediaGroups() {
	now := time.Now()
	var toDelete []string

	b.mediaGroups.Range(func(key, value interface{}) bool {
		mediaGroupID := key.(string)
		cache := value.(*MediaGroupCache)

		// 清理已处理超过30分钟的缓存
		if cache.Processed && now.Sub(cache.ProcessedAt) > 30*time.Minute {
			toDelete = append(toDelete, mediaGroupID)
		}
		// 清理超时未处理的缓存（5分钟）
		if !cache.Processed && now.Sub(cache.LastUpdate) > 5*time.Minute {
			toDelete = append(toDelete, mediaGroupID)
		}

		return true
	})

	// 删除过期的缓存
	for _, mediaGroupID := range toDelete {
		b.mediaGroups.Delete(mediaGroupID)
		logger.WithField("media_group_id", mediaGroupID).Debug("Cleaned up expired media group cache")
	}

	if len(toDelete) > 0 {
		logger.WithField("cleaned_count", len(toDelete)).Info("Cleaned up expired media group caches")
	}
}

// ProcessMessage 处理消息
func (b *BotService) ProcessMessage(message *models.Message) {
	if message == nil || message.Chat == nil {
		return
	}

	logger.WithFields(map[string]interface{}{
		"chat_id":        message.Chat.ID,
		"message":        message.Text,
		"caption":        message.Caption,
		"media_group_id": message.MediaGroupID,
		"photo_count":    len(message.Photo),
	}).Info("Processing message")

	// 确定群组类型
	groupType := b.getGroupType(message.Chat.ID)

	switch groupType {
	case models.GroupTypeMerchant:
		// 如果是Media Group消息，需要特殊处理
		if message.MediaGroupID != "" {
			b.processMediaGroupMessage(message)
		} else {
			b.processMerchantMessage(message)
		}
	case models.GroupTypeSupplier:
		b.processSupplierMessage(message)
	case models.GroupTypeCustomerService:
		// 人工客服群不需要自动处理
		logger.Info("Message from customer service group, no action needed")
	default:
		logger.WithField("chat_id", message.Chat.ID).Warn("Unknown group type or group is not actived")
	}
}

// processMediaGroupMessage 处理Media Group消息
func (b *BotService) processMediaGroupMessage(message *models.Message) {
	mediaGroupID := message.MediaGroupID

	logger.WithFields(map[string]interface{}{
		"media_group_id": mediaGroupID,
		"message_id":     message.MessageID,
		"caption":        message.Caption,
		"photo_count":    len(message.Photo),
	}).Info("Processing media group message")

	// 获取或创建Media Group缓存
	cacheInterface, _ := b.mediaGroups.LoadOrStore(mediaGroupID, &MediaGroupCache{
		Messages:   make([]*MediaGroupMessage, 0),
		LastUpdate: time.Now(),
		Processing: false,
		Processed:  false,
	})
	cache := cacheInterface.(*MediaGroupCache)

	// 检查是否已经处理过
	if cache.Processed {
		logger.WithFields(map[string]interface{}{
			"media_group_id": mediaGroupID,
			"message_id":     message.MessageID,
		}).Debug("Media group already processed, ignoring message")
		return
	}

	// 添加消息到缓存
	cache.Messages = append(cache.Messages, &MediaGroupMessage{
		Message:   message,
		Timestamp: time.Now(),
	})

	// 如果消息有caption，更新最后的caption
	if message.Caption != "" {
		cache.LastCaption = message.Caption
	}

	cache.LastUpdate = time.Now()

	// 只有当没有正在处理时才启动延迟处理
	if !cache.Processing {
		cache.Processing = true
		logger.WithField("media_group_id", mediaGroupID).Debug("Starting delayed processing for media group")

		// 启动智能延迟处理
		go b.smartDelayedProcessing(mediaGroupID)
	} else {
		logger.WithField("media_group_id", mediaGroupID).Debug("Media group already being processed, skipping delayed processing")
	}
}

// smartDelayedProcessing 智能延迟处理Media Group
func (b *BotService) smartDelayedProcessing(mediaGroupID string) {
	const (
		initialDelay  = 1 * time.Second        // 初始延迟
		maxDelay      = 5 * time.Second        // 最大延迟
		checkInterval = 500 * time.Millisecond // 检查间隔
	)

	startTime := time.Now()
	lastMessageCount := 0
	stableCount := 0 // 消息数量稳定的次数

	// 初始延迟
	time.Sleep(initialDelay)

	for {
		cacheInterface, exists := b.mediaGroups.Load(mediaGroupID)
		if !exists {
			logger.WithField("media_group_id", mediaGroupID).Warn("Media group cache disappeared during smart processing")
			return
		}

		cache := cacheInterface.(*MediaGroupCache)
		currentMessageCount := len(cache.Messages)

		// 检查消息数量是否稳定
		if currentMessageCount == lastMessageCount {
			stableCount++
		} else {
			stableCount = 0
			lastMessageCount = currentMessageCount
		}

		// 如果消息数量稳定超过2次检查（1.5秒）或者达到最大延迟时间，开始处理
		if stableCount >= 3 || time.Since(startTime) >= maxDelay {
			logger.WithFields(map[string]interface{}{
				"media_group_id": mediaGroupID,
				"message_count":  currentMessageCount,
				"stable_count":   stableCount,
				"elapsed_time":   time.Since(startTime),
				"trigger_reason": map[bool]string{true: "stable_messages", false: "max_delay"}[stableCount >= 3],
			}).Debug("Starting media group processing")

			b.processCompleteMediaGroup(mediaGroupID)
			return
		}

		// 继续等待
		time.Sleep(checkInterval)
	}
}

// processCompleteMediaGroup 处理完整的Media Group
func (b *BotService) processCompleteMediaGroup(mediaGroupID string) {
	cacheInterface, exists := b.mediaGroups.Load(mediaGroupID)
	if !exists {
		logger.WithField("media_group_id", mediaGroupID).Warn("Media group cache not found")
		return
	}

	cache := cacheInterface.(*MediaGroupCache)

	// 检查是否已经处理过
	if cache.Processed {
		logger.WithField("media_group_id", mediaGroupID).Debug("Media group already processed, skipping")
		return
	}

	// 标记为已处理，防止重复处理
	cache.Processed = true
	cache.ProcessedAt = time.Now()
	cache.Processing = false

	logger.WithFields(map[string]interface{}{
		"media_group_id": mediaGroupID,
		"message_count":  len(cache.Messages),
		"last_caption":   cache.LastCaption,
	}).Info("Processing complete media group")

	// 获取最后一条消息用于回复（无论是否为订单消息都需要）
	lastMessage := cache.Messages[len(cache.Messages)-1].Message

	// 检查是否为订单消息（caption包含订单号）
	logger.WithFields(map[string]interface{}{
		"media_group_id": mediaGroupID,
		"last_caption":   cache.LastCaption,
		"caption_empty":  cache.LastCaption == "",
	}).Debug("Checking if media group is an order message")

	if cache.LastCaption == "" {
		logger.WithField("media_group_id", mediaGroupID).Debug("Media group has no caption, not an order message")
		return
	}

	if !b.parser.IsMediaGroupOrderMessage(cache.LastCaption) {
		logger.WithFields(map[string]interface{}{
			"media_group_id": mediaGroupID,
			"caption":        cache.LastCaption,
		}).Debug("Media group caption does not match order message pattern")
		return
	}

	// 标记消息正在处理
	b.markMessageAsProcessing(lastMessage.Chat.ID, int64(lastMessage.MessageID))

	// 如果是订单消息，立即标记为正在处理
	logger.WithFields(map[string]interface{}{
		"media_group_id":  mediaGroupID,
		"last_message_id": lastMessage.MessageID,
		"chat_id":         lastMessage.Chat.ID,
	}).Debug("About to mark media group message as processing")

	// 解析订单信息
	orderInfo := b.parser.ParseMediaGroupOrderInfo(cache.Messages, cache.LastCaption)
	if orderInfo == nil {
		logger.WithField("media_group_id", mediaGroupID).Error("Failed to parse media group order info")
		return
	}

	// 查询订单信息
	orderResponse, err := b.orderService.QueryOrder(orderInfo.OrderNumber)
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"order_number": orderInfo.OrderNumber,
			"error":        err,
		}).Error("Failed to query order for media group")
		b.forwardToCustomerServiceWithMediaGroup(orderInfo.OrderNumber, "Order query failed", lastMessage, cache.Messages)
		return
	}

	if !orderResponse.Success || orderResponse.Data == nil {
		logger.WithField("order_number", orderInfo.OrderNumber).Warn("Order not found for media group")
		b.forwardToCustomerServiceWithMediaGroup(orderInfo.OrderNumber, "Order not found", lastMessage, cache.Messages)
		return
	}

	if strings.ToLower(orderResponse.Data.Status) == "success" {
		b.sendSuccessMessage(lastMessage.Chat.ID, lastMessage.MessageID, orderResponse.Data.MerchantOrderID)
		return
	}

	if strings.ToLower(orderResponse.Data.Status) == "fail" {
		b.sendFailedMessage(lastMessage.Chat.ID, lastMessage.MessageID, orderResponse.Data.MerchantOrderID)
		return

	}

	// 处理Media Group订单
	b.processMediaGroupOrder(orderInfo, orderResponse.Data, lastMessage, cache.Messages)

	// 注意：不再立即删除缓存，让定期清理机制处理
	logger.WithField("media_group_id", mediaGroupID).Debug("Media group processing completed, cache will be cleaned up later")
}

// markMessageAsProcessing 标记消息正在处理
func (b *BotService) markMessageAsProcessing(chatID int64, messageID int64) {
	logger.WithFields(map[string]interface{}{
		"chat_id":                chatID,
		"message_id":             messageID,
		"enable_status_messages": b.config.EnableStatusMessages,
	}).Debug("Marking message as processing")

	if b.config.EnableStatusMessages {
		statusMessage := "⏳ Processing media group order..."
		if err := b.telegramService.SendMessage(chatID, statusMessage, messageID); err != nil {
			logger.WithField("error", err).Warn("Failed to send media group processing status message")
		} else {
			logger.WithField("message", statusMessage).Debug("Successfully sent detailed processing status message")
		}
	} else {
		// 默认发送简洁的处理状态消息（引用原消息）
		statusMessage := "⏳ Processing..."
		if err := b.telegramService.SendMessage(chatID, statusMessage, messageID); err != nil {
			logger.WithField("error", err).Warn("Failed to send processing status message")
		} else {
			logger.WithField("message", statusMessage).Debug("Successfully sent simple processing status message")
		}
	}
}

// processMediaGroupOrder 处理Media Group订单
func (b *BotService) processMediaGroupOrder(orderInfo *models.OrderInfo, orderData *models.ThirdPartyOrderData, message *models.Message, mediaGroupMessages []*MediaGroupMessage) {
	logger.WithFields(map[string]interface{}{
		"order_number":   orderInfo.OrderNumber,
		"media_group_id": orderInfo.MediaGroupID,
		"image_count":    len(orderInfo.ImageFileIDs),
		"order_status":   orderData.Status,
	}).Info("Processing media group order")

	// 创建订单处理状态
	state := &models.OrderProcessingState{
		OrderNumber: orderInfo.OrderNumber,
		ChatID:      message.Chat.ID,
		MessageID:   (message.MessageID),
		Status:      "processing",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	b.processingOrders.Store(orderInfo.OrderNumber, state)

	// 处理每张图片的二维码
	totalAmount := decimal.Zero
	for i, imageFileID := range orderInfo.ImageFileIDs {
		logger.WithFields(map[string]interface{}{
			"image_index": i + 1,
			"image_id":    imageFileID,
		}).Debug("Processing media group image")

		// 下载图片
		fileInfo, err := b.telegramService.GetFile(imageFileID)
		if err != nil {
			logger.WithFields(map[string]interface{}{
				"image_index": i + 1,
				"image_id":    imageFileID,
				"error":       err,
			}).Error("Failed to get file info for media group image")
			additionalInfo := b.buildOrderInfoMessage(fmt.Sprintf("Failed to download image %d", i+1), orderData)
			b.forwardToCustomerServiceWithMediaGroup(orderData.SubmitOrderID, additionalInfo, message, mediaGroupMessages)
			return
		}

		imageData, err := b.telegramService.DownloadFile(fileInfo.FilePath)
		if err != nil {
			logger.WithFields(map[string]interface{}{
				"image_index": i + 1,
				"image_id":    imageFileID,
				"error":       err,
			}).Error("Failed to download media group image")
			additionalInfo := b.buildOrderInfoMessage(fmt.Sprintf("Failed to download image %d", i+1), orderData)
			b.forwardToCustomerServiceWithMediaGroup(orderData.SubmitOrderID, additionalInfo, message, mediaGroupMessages)
			return
		}

		// 扫描二维码
		qrInfo, err := b.qrService.ScanQRCode(imageData)
		if err != nil {
			logger.WithFields(map[string]interface{}{
				"image_index": i + 1,
				"image_id":    imageFileID,
				"error":       err,
			}).Error("Failed to scan QR code for media group image")
			additionalInfo := b.buildOrderInfoMessage(fmt.Sprintf("Failed to scan QR code in image %d", i+1), orderData)
			b.forwardToCustomerServiceWithMediaGroup(orderData.SubmitOrderID, additionalInfo, message, mediaGroupMessages)
			return
		}

		// 检查是否找到二维码
		if !qrInfo.Found {
			logger.WithFields(map[string]interface{}{
				"image_index": i + 1,
				"image_id":    imageFileID,
			}).Warn("No QR code found in media group image")
			additionalInfo := b.buildOrderInfoMessage(fmt.Sprintf("No QR code found in image %d", i+1), orderData)
			b.forwardToCustomerServiceWithMediaGroup(orderData.SubmitOrderID, additionalInfo, message, mediaGroupMessages)
			return
		}

		// 验证二维码并获取金额
		amount, err := b.verifyMediaGroupQRCode(qrInfo, orderData, i+1)
		if err != nil {
			logger.WithFields(map[string]interface{}{
				"image_index": i + 1,
				"error":       err,
			}).Error("Failed to verify QR code for media group image")

			// 检查是否为账户不匹配错误
			if strings.Contains(err.Error(), "account mismatch") {
				// 账户不匹配，发送警告消息给商户群
				b.sendAccountMismatchMessage(message.Chat.ID, int(message.MessageID), orderData.MerchantOrderID)
				return
			}

			// 其他错误转发给客服
			additionalInfo := b.buildOrderInfoMessage(fmt.Sprintf("QR code verification failed for image %d: %v", i+1, err), orderData)
			b.forwardToCustomerServiceWithMediaGroup(orderData.SubmitOrderID, additionalInfo, message, mediaGroupMessages)
			return
		}

		totalAmount = totalAmount.Add(amount)
		logger.WithFields(map[string]interface{}{
			"image_index":  i + 1,
			"image_amount": amount.String(),
			"total_amount": totalAmount.String(),
		}).Debug("Added amount from media group image")
	}

	// 验证总金额是否匹配订单金额
	b.verifyMediaGroupTotalAmount(totalAmount, orderData, message, mediaGroupMessages)

	// 更新订单状态为完成
	if state, ok := b.processingOrders.Load(orderInfo.OrderNumber); ok {
		orderState := state.(*models.OrderProcessingState)
		orderState.Status = "completed"
		orderState.UpdatedAt = time.Now()
	}
}

// verifyMediaGroupQRCode 验证Media Group中的二维码并返回金额
func (b *BotService) verifyMediaGroupQRCode(qrInfo *models.QRCodeInfo, orderData *models.ThirdPartyOrderData, imageIndex int) (decimal.Decimal, error) {
	// 调用二维码API验证凭证
	qrResponse, err := b.orderService.VerifyVoucher(qrInfo.Content)
	if err != nil {
		return decimal.Zero, fmt.Errorf("API call failed: %v", err)
	}

	// 检查API响应状态
	if !qrResponse.Status || qrResponse.Code != 100 || qrResponse.Data == nil || qrResponse.Data.StatusCode != "0000" {
		return decimal.Zero, fmt.Errorf("verification failed - status: %v, code: %d, statusCode: %s",
			qrResponse.Status, qrResponse.Code, qrResponse.Data.StatusCode)
	}

	// 解析金额（使用辅助函数处理嵌套结构）
	apiData := qrResponse.Data
	amountStr, err := b.getQRCodeAmount(apiData)
	if err != nil {
		return decimal.Zero, fmt.Errorf("failed to get amount: %v", err)
	}

	amount, err := decimal.NewFromString(amountStr)
	if err != nil {
		return decimal.Zero, fmt.Errorf("invalid amount format: %s", amountStr)
	}

	// 验证付款账户（使用辅助函数处理嵌套结构）
	senderAccount := b.getQRCodeSenderAccount(apiData)
	if senderAccount != "" && !b.isAccountMatched(senderAccount, orderData.PaymentAccount) {
		logger.WithFields(map[string]interface{}{
			"image_index":   imageIndex,
			"api_account":   senderAccount,
			"order_account": orderData.PaymentAccount,
		}).Warn("Account mismatch in media group verification")
		// 对于Media Group，账户不匹配也返回失败
		return decimal.Zero, fmt.Errorf("account mismatch in image %d - API account: %s, Order account: %s",
			imageIndex, senderAccount, orderData.PaymentAccount)
	}

	logger.WithFields(map[string]interface{}{
		"image_index": imageIndex,
		"amount":      amount.String(),
		"api_account": senderAccount,
	}).Debug("Media group QR code verified successfully")

	return amount, nil
}

// verifyMediaGroupTotalAmount 验证Media Group总金额
func (b *BotService) verifyMediaGroupTotalAmount(totalAmount decimal.Decimal, orderData *models.ThirdPartyOrderData, message *models.Message, mediaGroupMessages []*MediaGroupMessage) {
	if orderData.ActualAmount == "" {
		logger.Warn("Order actual amount is empty, cannot verify media group total amount")
		b.forwardToCustomerServiceWithMediaGroup(orderData.SubmitOrderID, "Order actual amount is empty", message, mediaGroupMessages)
		return
	}

	orderAmount, err := decimal.NewFromString(orderData.ActualAmount)
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"actual_amount": orderData.ActualAmount,
			"error":         err,
		}).Error("Failed to parse order actual amount for media group")
		b.forwardToCustomerServiceWithMediaGroup(orderData.SubmitOrderID, "Invalid order amount format", message, mediaGroupMessages)
		return
	}

	logger.WithFields(map[string]interface{}{
		"total_qr_amount": totalAmount.String(),
		"order_amount":    orderAmount.String(),
		"order_number":    orderData.MerchantOrderID,
		"media_group":     true,
	}).Info("Comparing media group total amount")

	// 使用相同的金额比较逻辑（支持三种比较方式）
	if b.isAmountMatched(totalAmount.String(), orderData.ActualAmount, orderData.Amount) {
		// 金额匹配，检查订单状态

		if strings.ToLower(orderData.Status) == "unpay" {
			b.forwardToSupplier(orderData, message)
		} else {
			logger.WithField("status", orderData.Status).Warn("Unknown order status for media group")
			b.forwardToCustomerServiceWithMediaGroup(orderData.SubmitOrderID, "", message, mediaGroupMessages)
		}

	} else {
		// 金额不匹配，发送不匹配消息
		b.sendAmountMismatchMessage(message.Chat.ID, message.MessageID, orderData.MerchantOrderID)
	}
}

// processMerchantMessage 处理商户群消息
func (b *BotService) processMerchantMessage(message *models.Message) {
	// 检查是否为账户询问消息
	if b.parser.IsAccountInquiry(message) {

		// 默认发送简洁的处理状态消息（引用原消息）
		statusMessage := "⏳ Processing, please wait..."
		if err := b.telegramService.SendMessage(message.Chat.ID, statusMessage, message.MessageID); err != nil {
			logger.WithField("error", err).Warn("Failed to send account inquiry status message")
		}
		// 是账户询问消息，转发到客服群
		logger.WithField("chat_id", message.Chat.ID).Info("Account inquiry message, forwarding to customer service")
		b.forwardToCustomerService("", "Account inquiry from merchant group", message)
		return
	}

	// 检查是否为订单消息
	if !b.parser.IsOrderMessage(message) {
		// 不是订单消息，直接忽略
		logger.WithField("chat_id", message.Chat.ID).Debug("Non-order message, ignoring")
		return
	}

	statusMessage := "⏳ Processing..."
	if err := b.telegramService.SendMessage(message.Chat.ID, statusMessage, message.MessageID); err != nil {
		logger.WithField("error", err).Warn("Failed to send processing status message")
	}

	// 解析订单信息
	orderInfo := b.parser.ParseOrderInfo(message)
	if orderInfo == nil || orderInfo.OrderNumber == "" {
		logger.Error("Failed to parse order info from message, forwarding to customer service")
		b.forwardToCustomerService("", "Failed to parse order info", message)
		return
	}

	logger.WithField("order_number", orderInfo.OrderNumber).Info("Processing order")

	// 发送处理状态消息（引用原消息）
	/* if b.config.EnableStatusMessages {
		statusMessage := fmt.Sprintf("⏳ Processing order: %s", orderInfo.OrderNumber)
		if err := b.telegramService.SendMessage(message.Chat.ID, statusMessage, message.MessageID); err != nil {
			logger.WithField("error", err).Warn("Failed to send status message, but continuing")
		}
	} else { */
	// 默认发送简洁的处理状态消息（引用原消息）

	//}

	// 记录订单处理开始
	logger.WithFields(map[string]interface{}{
		"order_number": orderInfo.OrderNumber,
		"chat_id":      message.Chat.ID,
		"message_id":   message.MessageID,
	}).Info("Starting order processing")

	// 创建订单处理状态
	state := &models.OrderProcessingState{
		OrderNumber: orderInfo.OrderNumber,
		ChatID:      message.Chat.ID,
		MessageID:   (message.MessageID),
		Status:      "processing",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	b.processingOrders.Store(orderInfo.OrderNumber, state)

	// 开始处理订单
	go b.processOrder(orderInfo, message)
}

// processSupplierMessage 处理供应商群消息
func (b *BotService) processSupplierMessage(message *models.Message) {
	// 检查是否为供应商回复
	if !b.parser.IsSupplierReply(message) {
		return
	}

	// 解析供应商回复
	reply := b.parser.ParseSupplierReply(message)
	if reply == nil {
		return
	}

	logger.WithField("submit_order_id", reply.SubmitOrderID).Info("Processing supplier reply")

	// 发送确认消息（引用原消息）
	confirmMessage := "✅ Reply received"
	if err := b.telegramService.SendMessage(message.Chat.ID, confirmMessage, message.MessageID); err != nil {
		logger.WithField("error", err).Warn("Failed to send supplier reply confirmation message")
	}

	// 处理供应商回复
	go b.processSupplierReply(reply)
}

// getGroupType 获取群组类型
func (b *BotService) getGroupType(chatID int64) models.GroupType {
	// 先从缓存中查找
	if groupType, ok := b.groupTypes.Load(chatID); ok {
		return groupType.(models.GroupType)
	}

	// 从数据库获取群组类型
	telegramGroupID := strconv.FormatInt(chatID, 10)
	if b.groupService != nil {
		if group, err := b.groupService.GetGroupByTelegramID(telegramGroupID); err == nil && group != nil {

			if !group.IsActive {
				return models.GroupNotActive
			}
			// 缓存数据库中的群组类型
			b.groupTypes.Store(chatID, group.GroupType)
			return group.GroupType
		}
	}

	// 如果数据库中没有找到，默认认为是商户群并在数据库中插入记录
	// 先从Telegram API获取群组名称
	var groupName string
	if chatInfo, err := b.telegramService.GetChat(chatID); err == nil && chatInfo != nil && chatInfo.Title != "" {
		groupName = chatInfo.Title
	} else {
		// 如果API调用失败，使用默认名称
		groupName = fmt.Sprintf("Merchant Group %d", chatID)
		logger.WithFields(map[string]interface{}{
			"chat_id": chatID,
			"error":   err,
		}).Warn("Failed to get group name from Telegram API, using default name")
	}

	newGroup := &models.Group{
		GroupName:       groupName,
		TelegramGroupID: telegramGroupID,
		GroupType:       models.GroupTypeMerchant,
		IsActive:        false,
	}

	if err := b.groupService.CreateGroup(newGroup); err != nil {
		logger.WithFields(map[string]interface{}{
			"chat_id": chatID,
			"error":   err,
		}).Error("Failed to create auto merchant group in database")
	} else {
		logger.WithFields(map[string]interface{}{
			"chat_id":    chatID,
			"group_id":   newGroup.ID,
			"group_name": newGroup.GroupName,
		}).Info("Auto-created merchant group in database")
	}

	b.groupTypes.Store(chatID, models.GroupTypeMerchant)
	return models.GroupTypeMerchant
}

// getQRCodeAmount 从QR码响应中获取金额（处理嵌套结构）
func (b *BotService) getQRCodeAmount(apiData *models.QRCodeScanData) (string, error) {
	// 优先从嵌套的data.data中获取
	if apiData.Data != nil && apiData.Data.Amount > 0 {
		return fmt.Sprintf("%.2f", apiData.Data.Amount), nil
	}

	// 回退到直接字段
	if apiData.Amount != "" {
		return apiData.Amount, nil
	}

	return "", fmt.Errorf("no amount found in QR code response")
}

// getQRCodeSenderAccount 从QR码响应中获取发送方账户（处理嵌套结构）
func (b *BotService) getQRCodeSenderAccount(apiData *models.QRCodeScanData) string {
	// 优先从嵌套的data.data中获取
	if apiData.Data != nil && apiData.Data.Sender.Account.Value != "" {
		return apiData.Data.Sender.Account.Value
	}

	// 回退到直接字段
	if apiData.Sender.Account.Value != "" {
		return apiData.Sender.Account.Value
	}

	return ""
}

// getGroupName 获取群组名称用于显示
func (b *BotService) getGroupName(chatID int64) string {
	// 先从缓存中查找
	if cachedName, ok := b.groupNames.Load(chatID); ok {
		return cachedName.(string)
	}

	// 优先从数据库获取群组名称
	telegramGroupID := strconv.FormatInt(chatID, 10)
	if b.groupService != nil {
		if dbGroupName, err := b.groupService.GetGroupName(telegramGroupID); err == nil && dbGroupName != "" {
			// 缓存数据库中的群组名称
			b.groupNames.Store(chatID, dbGroupName)
			return dbGroupName
		}
	}

	// 如果数据库中没有，尝试通过Telegram API获取群组信息
	chatInfo, err := b.telegramService.GetChat(chatID)
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"chat_id": chatID,
			"error":   err,
		}).Warn("Failed to get chat info from Telegram API")
	}

	var groupName string
	var groupType string

	// 从数据库获取群组信息 (reuse existing telegramGroupID variable)
	if b.groupService != nil {
		if group, err := b.groupService.GetGroupByTelegramID(telegramGroupID); err == nil && group != nil {
			// 根据数据库中的群组类型确定类型名称
			switch group.GroupType {
			case models.GroupTypeMerchant:
				groupType = "Merchant Group"
			case models.GroupTypeSupplier:
				groupType = "Supplier Group"
			case models.GroupTypeCustomerService:
				groupType = "Customer Service Group"
			default:
				groupType = "Unknown Group"
			}
		}
	}

	// 构建群组名称
	if chatInfo != nil && chatInfo.Title != "" {
		// 使用Telegram API获取的真实群组名称
		if groupType != "" {
			groupName = fmt.Sprintf("%s: %s", groupType, chatInfo.Title)
		} else {
			groupName = chatInfo.Title
		}
	} else {
		// 回退到群组ID
		if groupType != "" {
			groupName = fmt.Sprintf("%s (ID:%d)", groupType, chatID)
		} else {
			groupName = fmt.Sprintf("Group (ID:%d)", chatID)
		}
	}

	// 缓存结果
	b.groupNames.Store(chatID, groupName)

	logger.WithFields(map[string]interface{}{
		"chat_id":    chatID,
		"group_name": groupName,
		"api_title":  chatInfo != nil && chatInfo.Title != "",
	}).Debug("Resolved group name")

	return groupName
}

// processOrder 处理订单
func (b *BotService) processOrder(orderInfo *models.OrderInfo, message *models.Message) {
	defer func() {
		// 更新订单状态为完成
		if state, ok := b.processingOrders.Load(orderInfo.OrderNumber); ok {
			orderState := state.(*models.OrderProcessingState)
			orderState.Status = "completed"
			orderState.UpdatedAt = time.Now()
		}
	}()

	// 步骤1: 查询第三方API
	orderResponse, err := b.orderService.QueryOrder(orderInfo.OrderNumber)
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"order_number": orderInfo.OrderNumber,
			"error":        err,
		}).Error("Failed to query order, forwarding to customer service")
		b.forwardToCustomerService(orderInfo.OrderNumber, "Order query failed: "+err.Error(), message)
		return
	}

	// 步骤2: 检查订单是否存在
	if !orderResponse.Success || orderResponse.Data == nil {
		// 订单不存在，转发到客服群
		logger.WithField("order_number", orderInfo.OrderNumber).Warn("Order not found, forwarding to customer service")
		b.forwardToCustomerService(orderInfo.OrderNumber, "Order not found in system", message)
		return
	}

	if strings.ToLower(orderResponse.Data.Status) == "success" {
		b.sendSuccessMessage(message.Chat.ID, (message.MessageID), orderResponse.Data.MerchantOrderID)
		return
	}

	if strings.ToLower(orderResponse.Data.Status) == "fail" {
		b.sendFailedMessage(message.Chat.ID, (message.MessageID), orderResponse.Data.MerchantOrderID)
		return
	}

	orderData := orderResponse.Data
	logger.WithFields(map[string]interface{}{
		"merchant_order_id": orderData.MerchantOrderID,
		"status":            orderData.Status,
	}).Info("Found order")

	// 步骤3: 处理图片和二维码
	if orderInfo.HasImage {
		b.processOrderWithImage(orderInfo, orderData, message)
	} else {
		// 没有图片，直接转发给人工客服
		b.forwardToCustomerService(orderData.SubmitOrderID, "", message)
	}
}

// processOrderWithImage 处理包含图片的订单
func (b *BotService) processOrderWithImage(orderInfo *models.OrderInfo, orderData *models.ThirdPartyOrderData, message *models.Message) {
	// 下载图片
	fileInfo, err := b.telegramService.GetFile(orderInfo.ImageFileID)
	if err != nil {
		logger.WithField("error", err).Error("Failed to get file info")
		b.forwardToCustomerService(orderData.SubmitOrderID, "", message)
		return
	}

	imageData, err := b.telegramService.DownloadFile(fileInfo.FilePath)
	if err != nil {
		logger.WithField("error", err).Error("Failed to download image")
		b.forwardToCustomerService(orderData.SubmitOrderID, "", message)
		return
	}

	// 扫描二维码
	qrInfo, err := b.qrService.ScanQRCode(imageData)
	if err != nil {
		logger.WithField("error", err).Error("Failed to scan QR code")
		b.forwardToCustomerService(orderData.SubmitOrderID, "", message)
		return
	}

	// 检查是否找到二维码
	if !qrInfo.Found {
		// 没有二维码，转发给人工客服，附加订单信息
		additionalInfo := b.buildOrderInfoMessage("No QR code found", orderData)
		b.forwardToCustomerService(orderData.SubmitOrderID, additionalInfo, message)
		return
	}

	// 验证付款信息
	b.verifyPaymentInfo(qrInfo, orderData, message)
}

// verifyPaymentInfo 验证付款信息
func (b *BotService) verifyPaymentInfo(qrInfo *models.QRCodeInfo, orderData *models.ThirdPartyOrderData, message *models.Message) {
	// 首先调用二维码API验证凭证
	qrResponse, err := b.orderService.VerifyVoucher(qrInfo.Content)
	if err != nil {
		logger.WithField("error", err).Error("Failed to verify voucher via API")
		// API调用失败，转发给人工客服，附加订单信息
		additionalInfo := b.buildOrderInfoMessage("QR code verification API failed", orderData)
		b.forwardToCustomerService(orderData.SubmitOrderID, additionalInfo, message)
		return
	}

	// 检查API响应状态
	if !qrResponse.Status || qrResponse.Code != 100 || qrResponse.Data == nil || qrResponse.Data.StatusCode != "0000" {
		// 凭证验证失败，转发给人工客服
		logger.WithFields(map[string]interface{}{
			"status":      qrResponse.Status,
			"code":        qrResponse.Code,
			"status_code": qrResponse.Data.StatusCode,
			"message":     qrResponse.Message,
		}).Warn("QR code verification failed, forwarding to customer service")

		additionalInfo := b.buildOrderInfoMessage("QR code verification failed", orderData)
		b.forwardToCustomerService(orderData.SubmitOrderID, additionalInfo, message)
		return
	}

	// 凭证验证成功，进行进一步的信息验证
	apiData := qrResponse.Data

	// 验证付款账户（使用辅助函数处理嵌套结构）
	senderAccount := b.getQRCodeSenderAccount(apiData)
	if senderAccount != "" && !b.isAccountMatched(senderAccount, orderData.PaymentAccount) {
		b.sendAccountMismatchMessage(message.Chat.ID, int(message.MessageID), orderData.MerchantOrderID)
		return
	}

	// 验证金额（使用辅助函数处理嵌套结构，支持三种比较方式）
	amountStr, amountErr := b.getQRCodeAmount(apiData)
	if amountErr == nil && amountStr != "" && orderData.ActualAmount != "" {
		// 方式1：直接字符串比较
		if amountStr == orderData.ActualAmount {
			logger.WithFields(map[string]interface{}{
				"api_amount":   amountStr,
				"order_amount": orderData.ActualAmount,
				"match_type":   "exact_string",
			}).Debug("Amount matched by exact string comparison")
		} else {
			// 使用decimal进行精确的金额比较
			apiDecimal, apiErr := decimal.NewFromString(amountStr)
			orderDecimal, orderErr := decimal.NewFromString(orderData.ActualAmount)

			if apiErr != nil || orderErr != nil {
				logger.WithFields(map[string]interface{}{
					"api_amount":      amountStr,
					"order_amount":    orderData.ActualAmount,
					"api_parse_err":   apiErr,
					"order_parse_err": orderErr,
				}).Warn("Failed to parse amounts as decimal")
				b.sendAmountMismatchMessage(message.Chat.ID, message.MessageID, orderData.MerchantOrderID)
				return
			}

			// 方式2：amountStr向上取整等于orderData.ActualAmount
			apiCeil := apiDecimal.Ceil()
			// 方式3：amountStr向下取整等于orderData.ActualAmount
			apiFloor := apiDecimal.Floor()

			if apiCeil.Equal(orderDecimal) {
				logger.WithFields(map[string]interface{}{
					"api_amount":   amountStr,
					"order_amount": orderData.ActualAmount,
					"api_ceil":     apiCeil.String(),
					"match_type":   "ceil",
				}).Debug("Amount matched by ceiling comparison")
			} else if apiFloor.Equal(orderDecimal) {
				logger.WithFields(map[string]interface{}{
					"api_amount":   amountStr,
					"order_amount": orderData.ActualAmount,
					"api_floor":    apiFloor.String(),
					"match_type":   "floor",
				}).Debug("Amount matched by floor comparison")
			} else {
				// 所有比较方式都不匹配
				logger.WithFields(map[string]interface{}{
					"api_amount":    amountStr,
					"order_amount":  orderData.ActualAmount,
					"api_decimal":   apiDecimal.String(),
					"order_decimal": orderDecimal.String(),
					"api_ceil":      apiCeil.String(),
					"api_floor":     apiFloor.String(),
					"order_id":      orderData.MerchantOrderID,
				}).Warn("Amount mismatch detected - all comparison methods failed")
				b.sendAmountMismatchMessage(message.Chat.ID, message.MessageID, orderData.MerchantOrderID)
				return
			}
		}
	}

	// 检查订单状态（大小写不敏感）

	if strings.ToLower(orderData.Status) == "unpay" {
		b.forwardToSupplier(orderData, message)
	} else {
		logger.WithField("status", orderData.Status).Warn("Unknown order status")
		b.forwardToCustomerService(orderData.SubmitOrderID, "", message)
	}

}

// processSupplierReply 处理供应商回复
func (b *BotService) processSupplierReply(reply *SupplierReply) {
	if reply.IsSuccess {
		// 供应商确认成功，需要找到对应的商户群并发送成功消息
		b.handleSupplierSuccess(reply.SubmitOrderID)
	} else {
		// 供应商回复其他内容，转发给人工客服
		b.forwardSupplierReplyToCustomerService(reply)
	}
}

func (b *BotService) sendAccountMismatchMessage(chatID int64, messageID int, orderNumber string) {
	message := fmt.Sprintf("Order Number: %s\nAccount does not match in system. Please create a new order that matches the account in the system.", orderNumber)
	if err := b.telegramService.SendMessage(chatID, message, int64(messageID)); err != nil {
		logger.WithField("error", err).Error("Failed to send account mismatch message")
	}
}

func (b *BotService) sendAmountMismatchMessage(chatID int64, messageID int64, orderNumber string) {
	message := fmt.Sprintf("Order Number: %s\nThe amount does not match, please provide the correct order number again", orderNumber)
	if err := b.telegramService.SendMessage(chatID, message, messageID); err != nil {
		logger.WithField("error", err).Error("Failed to send amount mismatch message")
	}
}

// isAccountMatched 验证账户是否匹配
// qrAccount: 二维码返回的账户（可能是部分隐藏格式，如 xxx-x-x8256-x）
// orderAccount: 订单中的完整账户号
func (b *BotService) isAccountMatched(qrAccount, orderAccount string) bool {
	if qrAccount == "" || orderAccount == "" {
		return false
	}

	// 如果完全相同，直接返回true
	if qrAccount == orderAccount {
		return true
	}

	// 提取二维码账户中的连续数字
	qrDigits := b.extractContinuousDigits(qrAccount)
	// 提取订单账户中的连续数字
	orderDigits := b.extractContinuousDigits(orderAccount)

	// 检查二维码中的数字是否在订单账户中存在
	for _, qrDigit := range qrDigits {
		if len(qrDigit) >= 4 { // 只考虑长度>=4的数字串
			for _, orderDigit := range orderDigits {
				if strings.Contains(orderDigit, qrDigit) || strings.Contains(qrDigit, orderDigit) {
					return true
				}
			}
		}
	}

	return false
}

// isAmountMatched 验证金额是否匹配（支持三种比较方式）
func (b *BotService) isAmountMatched(apiAmount, payOrderAmount string, orderAmount string) bool {
	if apiAmount == "" || payOrderAmount == "" || orderAmount == "" {
		return false
	}

	// 方式1：直接字符串比较
	if apiAmount == payOrderAmount {
		logger.WithFields(map[string]interface{}{
			"api_amount":   apiAmount,
			"order_amount": payOrderAmount,
			"match_type":   "exact_string",
		}).Debug("Amount matched by exact string comparison")
		return true
	}

	// 使用decimal进行精确的金额比较
	apiDecimal, apiErr := decimal.NewFromString(apiAmount)
	orderDecimal, orderErr := decimal.NewFromString(orderAmount)

	if apiErr != nil || orderErr != nil {
		logger.WithFields(map[string]interface{}{
			"api_amount":      apiAmount,
			"order_amount":    orderAmount,
			"api_parse_err":   apiErr,
			"order_parse_err": orderErr,
		}).Warn("Failed to parse amounts as decimal")
		return false
	}

	// 方式2：apiAmount向上取整等于orderAmount
	apiCeil := apiDecimal.Ceil()
	if apiCeil.Equal(orderDecimal) {
		logger.WithFields(map[string]interface{}{
			"api_amount":   apiAmount,
			"order_amount": orderAmount,
			"api_ceil":     apiCeil.String(),
			"match_type":   "ceil",
		}).Debug("Amount matched by ceiling comparison")
		return true
	}

	// 方式3：apiAmount向下取整等于orderAmount
	apiFloor := apiDecimal.Floor()
	if apiFloor.Equal(orderDecimal) {
		logger.WithFields(map[string]interface{}{
			"api_amount":   apiAmount,
			"order_amount": orderAmount,
			"api_floor":    apiFloor.String(),
			"match_type":   "floor",
		}).Debug("Amount matched by floor comparison")
		return true
	}

	// 所有比较方式都不匹配
	logger.WithFields(map[string]interface{}{
		"api_amount":    apiAmount,
		"order_amount":  orderAmount,
		"api_decimal":   apiDecimal.String(),
		"order_decimal": orderDecimal.String(),
		"api_ceil":      apiCeil.String(),
		"api_floor":     apiFloor.String(),
	}).Debug("Amount mismatch - all comparison methods failed")

	return false
}

// extractContinuousDigits 提取字符串中的连续数字
func (b *BotService) extractContinuousDigits(account string) []string {
	// 使用正则表达式提取连续的数字
	re := regexp.MustCompile(`\d+`)
	return re.FindAllString(account, -1)
}

// buildOrderInfoMessage 构建包含订单信息的附加信息
func (b *BotService) buildOrderInfoMessage(reason string, orderData *models.ThirdPartyOrderData) string {
	return fmt.Sprintf("%s\n\nSystem Information:\nSubmit Order ID: %s\nPayment Institution: %s\nActual Amount: %s\nOrder Amount: %s\nPayment Account: %s\nOrder Status: %s",
		reason,
		orderData.SubmitOrderID,
		orderData.PaymentInstitution,
		orderData.ActualAmount,
		orderData.Amount,
		orderData.PaymentAccount,
		orderData.Status)
}

func (b *BotService) sendSuccessMessage(chatID int64, messageID int64, orderNumber string) {
	message := fmt.Sprintf("Order Number: %s\nSuccess", orderNumber)
	if err := b.telegramService.SendMessage(chatID, message, int64(messageID)); err != nil {
		logger.WithField("error", err).Error("Failed to send success message")
	}
}

func (b *BotService) sendFailedMessage(chatID int64, messageID int64, orderNumber string) {
	message := fmt.Sprintf("Order Number: %s\nFailed", orderNumber)
	if err := b.telegramService.SendMessage(chatID, message, int64(messageID)); err != nil {
		logger.WithField("error", err).Error("Failed to send failed message")
	}
}

// forwardToCustomerService 转发给人工客服群
func (b *BotService) forwardToCustomerService(submitOrderID string, additionalInfo string, originalMessage *models.Message) {
	b.forwardToCustomerServiceWithMediaGroup(submitOrderID, additionalInfo, originalMessage, nil)
}

// forwardToCustomerServiceWithMediaGroup 转发给人工客服群（支持多图相册）
func (b *BotService) forwardToCustomerServiceWithMediaGroup(submitOrderID string, additionalInfo string, originalMessage *models.Message, mediaGroupMessages []*MediaGroupMessage) {
	logger.WithFields(map[string]interface{}{
		"submit_order_id":   submitOrderID,
		"additional_info":   additionalInfo,
		"chat_id":           originalMessage.Chat.ID,
		"message_id":        originalMessage.MessageID,
		"media_group_count": len(mediaGroupMessages),
	}).Info("Starting to forward message to customer service")

	// 从数据库获取客服群
	customerServiceGroups, err := b.groupService.GetCustomerServiceGroups()
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"submit_order_id": submitOrderID,
			"additional_info": additionalInfo,
			"error":           err,
		}).Error("Failed to get customer service groups from database")
		return
	}

	if len(customerServiceGroups) == 0 {
		logger.WithFields(map[string]interface{}{
			"submit_order_id": submitOrderID,
			"additional_info": additionalInfo,
			"chat_id":         originalMessage.Chat.ID,
			"message_id":      originalMessage.MessageID,
		}).Error("No customer service groups configured - message cannot be forwarded")
		return
	}

	// 使用第一个客服群
	customerServiceGroup := customerServiceGroups[0]
	customerServiceGroupID, err := strconv.ParseInt(customerServiceGroup.TelegramGroupID, 10, 64)
	if err != nil {
		logger.WithField("error", err).Error("Invalid customer service group ID")
		return
	}

	// 构建转发内容：来源群组 + 原消息文本 + 失败原因
	var forwardContent string

	// 添加来源群组信息（使用Telegram API获取真实群组名称）
	sourceGroupName := b.getGroupName(originalMessage.Chat.ID)
	forwardContent = fmt.Sprintf("📍 Source: %s\n", sourceGroupName)

	// 获取原始消息文本（优先使用Text，如果没有则使用Caption）
	var originalContent string
	if originalMessage.Text != "" {
		originalContent = originalMessage.Text
	} else if originalMessage.Caption != "" {
		originalContent = originalMessage.Caption
	}

	// 如果是多图相册，尝试从最后一条消息获取caption
	if mediaGroupMessages != nil && originalContent == "" {
		for i := len(mediaGroupMessages) - 1; i >= 0; i-- {
			if mediaGroupMessages[i].Message.Caption != "" {
				originalContent = mediaGroupMessages[i].Message.Caption
				break
			}
		}
	}

	if originalContent != "" {
		forwardContent += originalContent
	}

	// 添加失败原因（放在底部）
	if additionalInfo != "" {
		if originalContent != "" {
			forwardContent += "\n\n"
		}
		forwardContent += "━━━━━━━━━━━━━━━━━━━━\n"
		forwardContent += additionalInfo
	}

	// 转发消息
	logger.WithFields(map[string]interface{}{
		"has_media_group":    mediaGroupMessages != nil && len(mediaGroupMessages) > 0,
		"media_group_count":  len(mediaGroupMessages),
		"has_original_photo": len(originalMessage.Photo) > 0,
		"forward_content":    forwardContent,
	}).Debug("Determining forward method")

	if mediaGroupMessages != nil && len(mediaGroupMessages) > 0 {
		// 多图相册：使用sendMediaGroup发送所有图片
		logger.WithField("media_group_count", len(mediaGroupMessages)).Info("Forwarding as media group")
		b.forwardMediaGroupToCustomerService(customerServiceGroupID, mediaGroupMessages, forwardContent)
	} else if len(originalMessage.Photo) > 0 {
		// 单张图片：转发图片+原始文本+失败原因作为说明
		largestPhoto := originalMessage.Photo[0]
		for _, photo := range originalMessage.Photo {
			if photo.FileSize > largestPhoto.FileSize {
				largestPhoto = photo
			}
		}

		if err := b.telegramService.SendPhoto(customerServiceGroupID, largestPhoto.FileID, forwardContent); err != nil {
			logger.WithField("error", err).Error("Failed to forward photo to customer service")
		}
	} else {
		// 无图片：转发原始文本+失败原因
		if forwardContent != "" {
			if err := b.telegramService.SendMessage(customerServiceGroupID, forwardContent, 0); err != nil {
				logger.WithField("error", err).Error("Failed to forward message to customer service")
			}
		}
	}
}

// forwardMediaGroupToCustomerService 转发多图相册到客服群
func (b *BotService) forwardMediaGroupToCustomerService(customerServiceGroupID int64, mediaGroupMessages []*MediaGroupMessage, caption string) {
	logger.WithFields(map[string]interface{}{
		"customer_service_group_id": customerServiceGroupID,
		"media_group_count":         len(mediaGroupMessages),
		"caption_length":            len(caption),
	}).Info("Starting media group forward to customer service")

	if len(mediaGroupMessages) == 0 {
		logger.Warn("No media group messages to forward")
		return
	}

	// 构建媒体组
	var mediaItems []MediaGroupItem
	for i, msgWrapper := range mediaGroupMessages {
		message := msgWrapper.Message
		if len(message.Photo) > 0 {
			// 选择最大尺寸的图片
			largestPhoto := message.Photo[0]
			for _, photo := range message.Photo {
				if photo.FileSize > largestPhoto.FileSize {
					largestPhoto = photo
				}
			}

			mediaItem := MediaGroupItem{
				Type:  "photo",
				Media: largestPhoto.FileID,
			}

			// 只在第一张图片上添加caption
			if i == 0 {
				mediaItem.Caption = caption
			}

			mediaItems = append(mediaItems, mediaItem)
		}
	}

	if len(mediaItems) == 0 {
		logger.Warn("No valid photos found in media group messages")
		return
	}

	logger.WithFields(map[string]interface{}{
		"customer_service_group_id": customerServiceGroupID,
		"media_count":               len(mediaItems),
	}).Info("Forwarding media group to customer service")

	// 发送多图相册
	if err := b.telegramService.SendMediaGroup(customerServiceGroupID, mediaItems); err != nil {
		logger.WithField("error", err).Error("Failed to forward media group to customer service")

		// 如果发送多图失败，回退到发送单张图片
		logger.Info("Falling back to sending individual photos")
		for i, item := range mediaItems {
			var photoCaption string
			if i == 0 {
				photoCaption = caption + fmt.Sprintf("\n\n📷 Image %d/%d", i+1, len(mediaItems))
			} else {
				photoCaption = fmt.Sprintf("📷 Image %d/%d", i+1, len(mediaItems))
			}

			if err := b.telegramService.SendPhoto(customerServiceGroupID, item.Media, photoCaption); err != nil {
				logger.WithFields(map[string]interface{}{
					"error":       err,
					"photo_index": i + 1,
				}).Error("Failed to send individual photo as fallback")
			}
		}
	}
}

// forwardToSupplier 转发给供应商群
func (b *BotService) forwardToSupplier(orderData *models.ThirdPartyOrderData, originalMessage *models.Message) {
	// 从数据库根据支付机构找到对应的供应商群
	supplierGroups, err := b.groupService.GetSupplierGroupsByPaymentInstitution(orderData.PaymentInstitution)
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"payment_institution": orderData.PaymentInstitution,
			"error":               err,
		}).Error("Failed to get supplier groups from database")
		b.forwardToCustomerService(orderData.SubmitOrderID, "Failed to get supplier groups", originalMessage)
		return
	}

	if len(supplierGroups) == 0 {
		logger.WithField("payment_institution", orderData.PaymentInstitution).Warn("No supplier group found for payment institution")
		b.forwardToCustomerService(orderData.SubmitOrderID, "No supplier group configured", originalMessage)
		return
	}

	// 使用第一个匹配的供应商群
	supplierGroup := supplierGroups[0]
	supplierGroupID := supplierGroup.TelegramGroupID

	groupID, err := strconv.ParseInt(supplierGroupID, 10, 64)
	if err != nil {
		logger.WithField("error", err).Error("Invalid supplier group ID")
		b.forwardToCustomerService(orderData.SubmitOrderID, "Invalid supplier group ID", originalMessage)
		return
	}

	// 更新订单状态，记录供应商群ID
	if state, ok := b.processingOrders.Load(orderData.MerchantOrderID); ok {
		orderState := state.(*models.OrderProcessingState)
		orderState.SubmitOrderID = orderData.SubmitOrderID
		orderState.SupplierGroupID = supplierGroupID
	}

	// 转发图片和提交订单号给供应商
	if len(originalMessage.Photo) > 0 {
		largestPhoto := originalMessage.Photo[0]
		for _, photo := range originalMessage.Photo {
			if photo.FileSize > largestPhoto.FileSize {
				largestPhoto = photo
			}
		}

		caption := fmt.Sprintf("Submit Order ID: %s", orderData.SubmitOrderID)
		if err := b.telegramService.SendPhoto(groupID, largestPhoto.FileID, caption); err != nil {
			logger.WithField("error", err).Error("Failed to forward photo to supplier")
		}
	}
}

// handleSupplierSuccess 处理供应商成功回复
func (b *BotService) handleSupplierSuccess(submitOrderID string) {
	// 查找对应的订单处理状态
	var targetOrder *models.OrderProcessingState
	var merchantOrderID string

	b.processingOrders.Range(func(key, value interface{}) bool {
		orderState := value.(*models.OrderProcessingState)
		if orderState.SubmitOrderID == submitOrderID {
			targetOrder = orderState
			merchantOrderID = key.(string)
			return false // 停止遍历
		}
		return true
	})

	if targetOrder == nil {
		logger.WithField("submit_order_id", submitOrderID).Warn("No order found for submit order ID")
		return
	}

	// 发送成功消息到原商户群
	b.sendSuccessMessage(targetOrder.ChatID, int64(targetOrder.MessageID), merchantOrderID)

	// 清理订单状态
	b.processingOrders.Delete(merchantOrderID)
}

// forwardSupplierReplyToCustomerService 转发供应商回复给人工客服
func (b *BotService) forwardSupplierReplyToCustomerService(reply *SupplierReply) {
	// 从数据库获取客服群
	customerServiceGroups, err := b.groupService.GetCustomerServiceGroups()
	if err != nil {
		logger.WithField("error", err).Error("Failed to get customer service groups from database")
		return
	}

	if len(customerServiceGroups) == 0 {
		logger.Error("No customer service groups configured")
		return
	}

	// 使用第一个客服群
	customerServiceGroup := customerServiceGroups[0]
	customerServiceGroupID, err := strconv.ParseInt(customerServiceGroup.TelegramGroupID, 10, 64)
	if err != nil {
		logger.WithField("error", err).Error("Invalid customer service group ID")
		return
	}

	// 构建转发消息，包含来源群组信息
	sourceGroupName := b.getGroupName(reply.ReplyMessage.Chat.ID)
	message := fmt.Sprintf("Source: %s\n Order ID: %s\n━━━━━━━━━━━━━━━━━━━━\n🔄 Supplier Reply: %s",
		sourceGroupName, reply.SubmitOrderID, reply.ReplyContent)

	// 如果原消息有图片，也转发图片
	if len(reply.OriginalMessage.Photo) > 0 {
		largestPhoto := reply.OriginalMessage.Photo[0]
		for _, photo := range reply.OriginalMessage.Photo {
			if photo.FileSize > largestPhoto.FileSize {
				largestPhoto = photo
			}
		}

		if err := b.telegramService.SendPhoto(customerServiceGroupID, largestPhoto.FileID, message); err != nil {
			logger.WithField("error", err).Error("Failed to forward supplier reply with photo to customer service")
		}
	} else {
		if err := b.telegramService.SendMessage(customerServiceGroupID, message, 0); err != nil {
			logger.WithField("error", err).Error("Failed to forward supplier reply to customer service")
		}
	}
}
