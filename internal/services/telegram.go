package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
)

// TelegramService Telegram API service
type TelegramService struct {
	botToken string
	baseURL  string
	client   *http.Client
}

// NewTelegramService creates a new Telegram service
func NewTelegramService(botToken string) *TelegramService {
	return &TelegramService{
		botToken: botToken,
		baseURL:  "https://api.telegram.org/bot" + botToken,
		client:   &http.Client{},
	}
}

// SendMessage sends message
func (t *TelegramService) SendMessage(chatID int64, text string, replyToMessageID int64) error {
	payload := map[string]interface{}{
		"chat_id": chatID,
		"text":    text,
	}

	if replyToMessageID > 0 {
		payload["reply_to_message_id"] = replyToMessageID
	}

	return t.makeRequest("sendMessage", payload)
}

// SendPhoto sends photo
func (t *TelegramService) SendPhoto(chatID int64, photoFileID string, caption string) error {
	payload := map[string]interface{}{
		"chat_id": chatID,
		"photo":   photoFileID,
	}

	if caption != "" {
		payload["caption"] = caption
	}

	return t.makeRequest("sendPhoto", payload)
}

// SendMediaGroup 发送多图相册
func (t *TelegramService) SendMediaGroup(chatID int64, media []MediaGroupItem) error {
	payload := map[string]interface{}{
		"chat_id": chatID,
		"media":   media,
	}

	return t.makeRequest("sendMediaGroup", payload)
}

// MediaGroupItem 多图相册中的单个媒体项
type MediaGroupItem struct {
	Type    string `json:"type"`
	Media   string `json:"media"`
	Caption string `json:"caption,omitempty"`
}

// AddReaction 添加表情反应 (已弃用，改为使用引用回复)
func (t *TelegramService) AddReaction(chatID int64, messageID int, emoji string) error {
	// 此功能已弃用，直接返回错误以触发备选方案
	return fmt.Errorf("reaction feature disabled, use reply message instead")
}

// GetFile 获取文件信息
func (t *TelegramService) GetFile(fileID string) (*FileInfo, error) {
	payload := map[string]interface{}{
		"file_id": fileID,
	}

	resp, err := t.makeRequestWithResponse("getFile", payload)
	if err != nil {
		return nil, err
	}

	var result struct {
		OK     bool      `json:"ok"`
		Result *FileInfo `json:"result"`
	}

	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, err
	}

	if !result.OK {
		return nil, fmt.Errorf("telegram API error")
	}

	return result.Result, nil
}

// DownloadFile 下载文件
func (t *TelegramService) DownloadFile(filePath string) ([]byte, error) {
	url := fmt.Sprintf("https://api.telegram.org/file/bot%s/%s", t.botToken, filePath)

	resp, err := t.client.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	return io.ReadAll(resp.Body)
}

// SetWebhook 设置webhook
func (t *TelegramService) SetWebhook(webhookURL string) error {
	payload := map[string]interface{}{
		"url": webhookURL,
	}

	return t.makeRequest("setWebhook", payload)
}

// makeRequest 发送请求
func (t *TelegramService) makeRequest(method string, payload map[string]interface{}) error {
	_, err := t.makeRequestWithResponse(method, payload)
	return err
}

// makeRequestWithResponse 发送请求并返回响应
func (t *TelegramService) makeRequestWithResponse(method string, payload map[string]interface{}) ([]byte, error) {
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("%s/%s", t.baseURL, method)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := t.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("telegram API error: %s", string(body))
	}

	return body, nil
}

// GetChat 获取聊天信息
func (t *TelegramService) GetChat(chatID int64) (*ChatInfo, error) {
	payload := map[string]interface{}{
		"chat_id": chatID,
	}

	resp, err := t.makeRequestWithResponse("getChat", payload)
	if err != nil {
		return nil, fmt.Errorf("failed to make getChat request: %v", err)
	}

	var result struct {
		OK          bool      `json:"ok"`
		Result      *ChatInfo `json:"result"`
		ErrorCode   int       `json:"error_code,omitempty"`
		Description string    `json:"description,omitempty"`
	}

	if err := json.Unmarshal(resp, &result); err != nil {
		return nil, fmt.Errorf("failed to unmarshal getChat response: %v", err)
	}

	if !result.OK {
		// 处理常见的错误情况
		switch result.ErrorCode {
		case 400:
			return nil, fmt.Errorf("bad request: %s", result.Description)
		case 403:
			return nil, fmt.Errorf("bot was kicked from chat or insufficient permissions: %s", result.Description)
		case 404:
			return nil, fmt.Errorf("chat not found: %s", result.Description)
		default:
			return nil, fmt.Errorf("telegram API error (code %d): %s", result.ErrorCode, result.Description)
		}
	}

	return result.Result, nil
}

// ChatInfo 聊天信息结构
type ChatInfo struct {
	ID          int64  `json:"id"`
	Type        string `json:"type"`
	Title       string `json:"title,omitempty"`
	Username    string `json:"username,omitempty"`
	FirstName   string `json:"first_name,omitempty"`
	LastName    string `json:"last_name,omitempty"`
	Description string `json:"description,omitempty"`
}

// FileInfo 文件信息结构
type FileInfo struct {
	FileID       string `json:"file_id"`
	FileUniqueID string `json:"file_unique_id"`
	FileSize     int    `json:"file_size"`
	FilePath     string `json:"file_path"`
}
