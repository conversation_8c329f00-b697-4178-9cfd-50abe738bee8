package services

import (
	"bytes"
	"fmt"
	"image"
	"telegram-order-bot/internal/models"

	_ "image/jpeg"
	_ "image/png"

	"github.com/makiuchi-d/gozxing"
	"github.com/makiuchi-d/gozxing/qrcode"
)

// QRCodeService QR code service
type QRCodeService struct {
	reader gozxing.Reader
}

// NewQRCodeService creates a new QR code service
func NewQRCodeService() *QRCodeService {
	return &QRCodeService{
		reader: qrcode.NewQRCodeReader(),
	}
}

// ScanQRCode scans QR code in image
func (q *QRCodeService) ScanQRCode(imageData []byte) (*models.QRCodeInfo, error) {
	qrInfo := &models.QRCodeInfo{
		Found: false,
	}

	// Decode image
	img, _, err := image.Decode(bytes.NewReader(imageData))
	if err != nil {
		return qrInfo, fmt.Errorf("failed to decode image: %v", err)
	}

	// Convert to gozxing BinaryBitmap
	bmp, err := gozxing.NewBinaryBitmapFromImage(img)
	if err != nil {
		return qrInfo, fmt.Errorf("failed to create binary bitmap: %v", err)
	}

	// Scan QR code
	result, err := q.reader.Decode(bmp, nil)
	if err != nil {
		// No QR code found, this is not an error
		return qrInfo, nil
	}

	// QR code found, return content directly
	qrInfo.Found = true
	qrInfo.Content = result.GetText()

	return qrInfo, nil
}
