package database

import (
	"fmt"
	"telegram-order-bot/internal/logger"
	"telegram-order-bot/internal/models"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

// Config 数据库配置
type Config struct {
	Host     string
	Port     string
	User     string
	Password string
	Database string
	Charset  string
}

// DB 全局数据库连接
var DB *gorm.DB

// InitDatabase 初始化数据库连接
func InitDatabase(config Config) error {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		config.User,
		config.Password,
		config.Host,
		config.Port,
		config.Database,
		config.Charset,
	)

	// 配置GORM日志
	gormConfig := &gorm.Config{
		Logger: gormLogger.Default.LogMode(gormLogger.Info),
	}

	var err error
	DB, err = gorm.Open(mysql.Open(dsn), gormConfig)
	if err != nil {
		logger.WithFields(map[string]interface{}{
			"host":     config.Host,
			"port":     config.Port,
			"database": config.Database,
			"error":    err,
		}).Error("Failed to connect to database")
		return fmt.Errorf("failed to connect to database: %v", err)
	}

	// 配置连接池
	sqlDB, err := DB.DB()
	if err != nil {
		logger.WithField("error", err).Error("Failed to get underlying sql.DB")
		return fmt.Errorf("failed to get underlying sql.DB: %v", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)                   // 最大空闲连接数
	sqlDB.SetMaxOpenConns(100)                  // 最大打开连接数
	sqlDB.SetConnMaxLifetime(time.Hour)         // 连接最大生存时间

	logger.WithFields(map[string]interface{}{
		"host":     config.Host,
		"port":     config.Port,
		"database": config.Database,
	}).Info("Database connected successfully")

	return nil
}

// AutoMigrate 自动迁移数据库表
func AutoMigrate() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	// 自动迁移所有模型
	err := DB.AutoMigrate(
		&models.Group{},
		// 可以在这里添加其他模型
	)

	if err != nil {
		logger.WithField("error", err).Error("Failed to auto migrate database")
		return fmt.Errorf("failed to auto migrate database: %v", err)
	}

	logger.Info("Database auto migration completed successfully")
	return nil
}

// GetDB 获取数据库连接
func GetDB() *gorm.DB {
	return DB
}

// CloseDatabase 关闭数据库连接
func CloseDatabase() error {
	if DB == nil {
		return nil
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	err = sqlDB.Close()
	if err != nil {
		logger.WithField("error", err).Error("Failed to close database connection")
		return err
	}

	logger.Info("Database connection closed successfully")
	return nil
}

// Ping 测试数据库连接
func Ping() error {
	if DB == nil {
		return fmt.Errorf("database not initialized")
	}

	sqlDB, err := DB.DB()
	if err != nil {
		return err
	}

	err = sqlDB.Ping()
	if err != nil {
		logger.WithField("error", err).Error("Database ping failed")
		return err
	}

	logger.Info("Database ping successful")
	return nil
}
