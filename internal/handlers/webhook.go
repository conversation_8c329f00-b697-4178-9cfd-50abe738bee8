package handlers

import (
	"net/http"
	"telegram-order-bot/internal/logger"
	"telegram-order-bot/internal/models"
	"telegram-order-bot/internal/services"

	"github.com/gin-gonic/gin"
)

// WebhookHandler webhook处理器
type WebhookHandler struct {
	botService *services.BotService
}

// NewWebhookHandler 创建新的webhook处理器
func NewWebhookHandler(botService *services.BotService) *WebhookHandler {
	return &WebhookHandler{
		botService: botService,
	}
}

// HandleWebhook 处理webhook请求
func (h *WebhookHandler) HandleWebhook(c *gin.Context) {
	var update models.TelegramUpdate

	if err := c.ShouldBindJSON(&update); err != nil {
		logger.WithField("error", err).Error("Error parsing webhook data")
		c.<PERSON><PERSON><PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid JSON"})
		return
	}

	// 记录接收到的更新
	logger.WithField("update_id", update.UpdateID).Info("Received update")

	// 处理消息
	if update.Message != nil {
		go h.botService.ProcessMessage(update.Message)
	}

	c.JSON(http.StatusOK, gin.H{"status": "ok"})
}
