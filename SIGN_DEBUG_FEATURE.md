# Sign签名调试功能

## 📋 功能说明

为了便于调试第三方API的签名问题，在 `generateSign` 函数中添加了详细的日志输出，可以查看签名生成的完整过程。

## 🔧 新增日志内容

### 1. 调试级别日志
```json
{
  "level": "debug",
  "msg": "Sign generation details",
  "sign_string": "mer_order_no=ORDER123&request_no=abc123&request_time=20250726&key=your_api_key",
  "params": {
    "mer_order_no": "ORDER123",
    "request_no": "abc123", 
    "request_time": "20250726"
  },
  "api_key": "your_api_key"
}
```

### 2. 信息级别日志
```json
{
  "level": "info",
  "msg": "Generated sign",
  "original_string": "mer_order_no=ORDER123&request_no=abc123&request_time=20250726&key=your_api_key",
  "final_sign": "d41d8cd98f00b204e9800998ecf8427e"
}
```

## 📊 签名生成流程

### 1. 参数收集和排序
```go
// 收集所有参数键并排序（排除sign字段）
var keys []string
for key := range params {
    if key != "sign" {
        keys = append(keys, key)
    }
}
sort.Strings(keys) // ASCII顺序排序
```

### 2. 参数拼接
```go
// 按ASCII顺序连接参数
var parts []string
for _, key := range keys {
    value := fmt.Sprintf("%v", params[key])
    if value != "" { // 只包含非空值
        parts = append(parts, fmt.Sprintf("%s=%s", key, value))
    }
}
```

### 3. 最终字符串构建
```go
// 连接所有参数并加上密钥
signString := strings.Join(parts, "&") + "&key=" + apiKey
```

### 4. MD5计算
```go
// 计算MD5
hash := md5.Sum([]byte(signString))
signResult := hex.EncodeToString(hash[:])
```

## 🔍 调试示例

### 输入参数
```go
params := map[string]interface{}{
    "mer_order_no": "TEST123456",
    "request_no":   "req789012",
    "request_time": "20250726",
}
apiKey := "your_secret_key"
```

### 日志输出
```json
{
  "level": "debug",
  "msg": "Sign generation details",
  "sign_string": "mer_order_no=TEST123456&request_no=req789012&request_time=20250726&key=your_secret_key",
  "params": {
    "mer_order_no": "TEST123456",
    "request_no": "req789012",
    "request_time": "20250726"
  },
  "api_key": "your_secret_key",
  "time": "2025-07-26T01:53:00Z"
}

{
  "level": "info", 
  "msg": "Generated sign",
  "original_string": "mer_order_no=TEST123456&request_no=req789012&request_time=20250726&key=your_secret_key",
  "final_sign": "a1b2c3d4e5f6789012345678901234567",
  "time": "2025-07-26T01:53:00Z"
}
```

## 🚀 使用方法

### 1. 启用调试日志
在 `.env` 文件中设置：
```env
LOG_LEVEL=debug
```

或在systemd服务中设置：
```ini
Environment="LOG_LEVEL=debug"
```

### 2. 查看日志
```bash
# 查看应用日志
tail -f /www/robot/logs/app.log | grep -E "(Sign generation|Generated sign)"

# 查看systemd日志
journalctl -u telegram-order-bot.service -f | grep -E "(Sign generation|Generated sign)"
```

### 3. 过滤特定订单
```bash
# 查看特定订单的签名日志
tail -f /www/robot/logs/app.log | grep "ORDER123"
```

## 🔧 故障排查

### 常见签名问题

1. **参数顺序错误**
   - 检查 `sign_string` 中参数是否按ASCII顺序排列
   - 确认所有参数都包含在签名字符串中

2. **API密钥错误**
   - 检查 `api_key` 字段是否正确
   - 确认密钥没有多余的空格或特殊字符

3. **参数值错误**
   - 检查 `params` 对象中的值是否正确
   - 确认时间格式是否符合要求（yyyyMMdd）

4. **编码问题**
   - 确认字符串编码为UTF-8
   - 检查是否有特殊字符需要转义

### 调试步骤

1. **对比原始字符串**
   ```bash
   # 复制日志中的 original_string
   # 手动计算MD5验证
   echo -n "your_sign_string_here" | md5sum
   ```

2. **验证参数完整性**
   - 检查所有必需参数是否都包含
   - 确认参数值格式正确

3. **API文档对照**
   - 对照第三方API文档验证签名算法
   - 确认参数名称和格式要求

## ⚠️ 安全注意事项

1. **生产环境**: 建议使用 `LOG_LEVEL=info` 避免敏感信息泄露
2. **API密钥**: 调试日志会包含API密钥，注意日志文件安全
3. **日志轮转**: 确保配置日志轮转避免磁盘空间问题

## 📈 性能影响

- **调试级别**: 轻微性能影响，仅在调试时启用
- **信息级别**: 几乎无性能影响
- **日志I/O**: 建议使用SSD存储日志文件

---

**版本**: v2.2.0+  
**更新时间**: 2025-07-26  
**适用环境**: 开发、测试、生产调试
