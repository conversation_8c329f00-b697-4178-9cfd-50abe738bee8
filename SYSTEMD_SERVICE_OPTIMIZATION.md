# Systemd服务配置优化指南

## 🚨 当前问题分析

### 1. 日志冲突
- **Go应用日志**: 输出到文件 + 控制台
- **Systemd日志**: 捕获控制台输出到journal
- **结果**: 重复日志记录，性能损耗

### 2. 环境配置风险
- **EnvironmentFile**: 如果文件不存在会导致服务启动失败
- **环境变量**: 可能被系统环境覆盖

## 🛠️ 优化方案

### 方案1: 纯文件日志（生产环境推荐）

```ini
# 优点：
# - 避免日志重复
# - 性能最佳
# - 日志集中管理

StandardOutput=null
StandardError=file:/www/robot/logs/error.log
Environment="LOG_CONSOLE=false"
```

**使用场景**: 生产环境，性能优先

### 方案2: 混合日志（调试环境）

```ini
# 优点：
# - 便于调试
# - systemctl logs 可查看
# - 开发友好

StandardOutput=journal
StandardError=journal
Environment="LOG_CONSOLE=true"
Environment="LOG_LEVEL=debug"
```

**使用场景**: 开发/测试环境

## 🔧 关键配置解释

### 环境文件配置
```ini
# 原配置（有风险）
EnvironmentFile=/www/robot/.env

# 优化配置（安全）
EnvironmentFile=-/www/robot/.env  # - 表示文件可选
```

### 日志级别控制
```ini
Environment="LOG_LEVEL=info"      # 生产环境
Environment="LOG_LEVEL=debug"     # 调试环境
```

### 安全设置
```ini
NoNewPrivileges=true    # 禁止提升权限
PrivateTmp=true        # 私有临时目录
ProtectSystem=strict   # 保护系统目录
ProtectHome=true       # 保护用户目录
ReadWritePaths=/www/robot/logs  # 允许写入日志目录
```

## 📊 性能对比

| 配置 | CPU使用 | 磁盘I/O | 调试便利性 | 生产适用性 |
|------|---------|---------|------------|------------|
| 纯文件日志 | 低 | 低 | 中 | ⭐⭐⭐⭐⭐ |
| 混合日志 | 中 | 中 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 当前配置 | 高 | 高 | ⭐⭐⭐⭐ | ⭐⭐ |

## 🚀 部署建议

### 1. 生产环境
```bash
# 使用纯文件日志配置
sudo cp telegram-order-bot-file-logging.service /etc/systemd/system/telegram-order-bot.service
sudo systemctl daemon-reload
sudo systemctl restart telegram-order-bot
```

### 2. 开发环境
```bash
# 使用混合日志配置
sudo cp telegram-order-bot-hybrid-logging.service /etc/systemd/system/telegram-order-bot.service
sudo systemctl daemon-reload
sudo systemctl restart telegram-order-bot
```

## 📝 日志查看命令

### 文件日志
```bash
# 应用日志
tail -f /www/robot/logs/app.log

# 错误日志
tail -f /www/robot/logs/error.log
```

### Systemd日志
```bash
# 服务日志
journalctl -u telegram-order-bot.service -f

# 最近日志
journalctl -u telegram-order-bot.service --since "1 hour ago"
```

## ⚠️ 注意事项

1. **日志轮转**: 确保配置logrotate防止日志文件过大
2. **权限检查**: 确保telegram-bot用户有日志目录写权限
3. **磁盘空间**: 监控日志目录磁盘使用情况
4. **备份策略**: 重要日志需要备份

## 🔍 故障排查

### 服务启动失败
```bash
# 检查服务状态
sudo systemctl status telegram-order-bot

# 查看详细错误
journalctl -u telegram-order-bot.service --no-pager
```

### 日志问题
```bash
# 检查日志目录权限
ls -la /www/robot/logs/

# 检查磁盘空间
df -h /www/robot/logs/
```

---

**建议**: 生产环境使用 `telegram-order-bot-file-logging.service`
