[Unit]
Description=Telegram Order Bot Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=telegram-bot
Group=telegram-bot
WorkingDirectory=/www/robot
ExecStart=/www/robot/telegram-order-bot-linux
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5

# 纯文件日志配置 - 避免与systemd冲突
StandardOutput=null
StandardError=file:/www/robot/logs/error.log
SyslogIdentifier=telegram-order-bot

# 环境配置
EnvironmentFile=-/www/robot/.env
Environment="LOG_CONSOLE=false"
Environment="LOG_FILE_PATH=/www/robot/logs/app.log"
Environment="LOG_LEVEL=info"

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/www/robot/logs

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
