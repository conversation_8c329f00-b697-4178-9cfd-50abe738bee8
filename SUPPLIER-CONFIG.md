# 供应商群组JSON配置说明

现在支持使用JSON格式配置供应商群组，提供更灵活和结构化的配置方式。

## 配置方式

### 方式1：JSON配置（推荐）

在 `.env` 文件中使用 `SUPPLIER_GROUPS_JSON` 环境变量：

```bash
SUPPLIER_GROUPS_JSON=[{"name":"TopPay Supplier Group","group_id":"-1001234567890","payment_institution":"TAIP496-Cloud168-TOPPAY"},{"name":"VaderPay Supplier Group","group_id":"-1001234567891","payment_institution":"VaderPay-superkapoo888"}]
```

### 方式2：传统环境变量（兼容）

如果不设置 `SUPPLIER_GROUPS_JSON`，系统会回退到传统的环境变量方式：

```bash
SUPPLIER_GROUP_TOPPAY=-1001234567890
SUPPLIER_GROUP_VADERPAY=-1001234567891
SUPPLIER_GROUP_THPAY=-1001234567892
SUPPLIER_GROUP_EXKUB=-1001234567893
```

## JSON配置结构

每个供应商群组包含以下字段：

```json
{
  "name": "供应商群聊名称",
  "group_id": "Telegram群组ID", 
  "payment_institution": "支付机构标识"
}
```

### 字段说明

- **name**: 供应商群聊的显示名称，用于日志和管理界面
- **group_id**: Telegram群组ID，必须是完整的群组ID（如：-1001234567890）
- **payment_institution**: 支付机构标识，用于匹配订单中的支付机构字段

## 使用步骤

### 1. 创建JSON配置文件

创建 `supplier-groups.json` 文件：

```json
[
  {
    "name": "TopPay Supplier Group",
    "group_id": "-1001234567890",
    "payment_institution": "TAIP496-Cloud168-TOPPAY"
  },
  {
    "name": "VaderPay Supplier Group", 
    "group_id": "-1001234567891",
    "payment_institution": "VaderPay-superkapoo888"
  }
]
```

### 2. 生成.env配置

运行生成脚本：

```bash
./generate-supplier-config.sh
```

### 3. 更新.env文件

将生成的 `SUPPLIER_GROUPS_JSON` 行添加到你的 `.env` 文件中。

### 4. 重启服务

```bash
./manage-service.sh restart
```

## 配置示例

### 完整的JSON配置示例

```json
[
  {
    "name": "TopPay Thailand",
    "group_id": "-1001234567890",
    "payment_institution": "TAIP496-Cloud168-TOPPAY"
  },
  {
    "name": "VaderPay Gateway",
    "group_id": "-1001234567891", 
    "payment_institution": "VaderPay-superkapoo888"
  },
  {
    "name": "THPay Cloud",
    "group_id": "-1001234567892",
    "payment_institution": "THPay-THPayCloudpay888"
  },
  {
    "name": "Exkub SCB",
    "group_id": "-1001234567893",
    "payment_institution": "ExkubScbRs-exkubscbIOne"
  },
  {
    "name": "Exkub GSB",
    "group_id": "-1001234567894",
    "payment_institution": "exkubgsb"
  }
]
```

### 对应的.env配置

```bash
SUPPLIER_GROUPS_JSON=[{"name":"TopPay Thailand","group_id":"-1001234567890","payment_institution":"TAIP496-Cloud168-TOPPAY"},{"name":"VaderPay Gateway","group_id":"-1001234567891","payment_institution":"VaderPay-superkapoo888"},{"name":"THPay Cloud","group_id":"-1001234567892","payment_institution":"THPay-THPayCloudpay888"},{"name":"Exkub SCB","group_id":"-1001234567893","payment_institution":"ExkubScbRs-exkubscbIOne"},{"name":"Exkub GSB","group_id":"-1001234567894","payment_institution":"exkubgsb"}]
```

## 优势

### JSON配置的优势

1. **结构化**: 清晰的数据结构，易于理解和维护
2. **扩展性**: 可以轻松添加新的字段
3. **可读性**: 包含群组名称，便于识别
4. **灵活性**: 支持任意数量的供应商群组
5. **验证**: 可以使用JSON工具验证格式正确性

### 传统配置的优势

1. **简单**: 每个群组一个环境变量
2. **兼容**: 与现有配置完全兼容
3. **直观**: 环境变量名直接对应群组

## 注意事项

1. **JSON格式**: 确保JSON格式正确，可以使用在线JSON验证工具
2. **群组ID**: 必须使用完整的Telegram群组ID
3. **支付机构**: payment_institution字段必须与API返回的支付机构字段完全匹配
4. **转义**: 在.env文件中，JSON字符串不需要额外转义
5. **优先级**: 如果设置了SUPPLIER_GROUPS_JSON，会忽略传统的环境变量

## 故障排除

### JSON格式错误

如果JSON格式有误，系统会回退到传统配置方式。检查：

1. 括号是否匹配
2. 引号是否正确
3. 逗号是否缺失或多余
4. 字段名是否正确

### 群组匹配失败

如果订单无法匹配到正确的供应商群组：

1. 检查payment_institution字段是否与API返回值匹配
2. 确认群组ID是否正确
3. 查看日志中的匹配过程

### 配置不生效

1. 确认.env文件已更新
2. 重启服务
3. 检查日志中的配置加载信息

## 工具脚本

- `generate-supplier-config.sh`: 从JSON文件生成.env配置
- `supplier-groups.json`: JSON配置模板
- `.env.example.json`: 完整的.env配置示例
