#!/bin/bash

# Telegram Bot Webhook Setup Script

BOT_TOKEN="**********************************************"
WEBHOOK_URL="https://cpcheckbot.thaipay.info/telegram-webhook"

echo "🔧 Setting up Telegram Bot Webhook..."
echo "Bot Token: ${BOT_TOKEN:0:10}..."
echo "Webhook URL: $WEBHOOK_URL"
echo ""

# Set webhook
echo "📡 Setting webhook..."
response=$(curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/setWebhook" \
     -H "Content-Type: application/json" \
     -d "{\"url\": \"$WEBHOOK_URL\"}")

echo "Response: $response"

# Check if successful
if echo "$response" | grep -q '"ok":true'; then
    echo "✅ Webhook set successfully!"
else
    echo "❌ Failed to set webhook"
    echo "Response: $response"
    exit 1
fi

echo ""
echo "🔍 Verifying webhook info..."
webhook_info=$(curl -s "https://api.telegram.org/bot$BOT_TOKEN/getWebhookInfo")
echo "$webhook_info" | python3 -m json.tool 2>/dev/null || echo "$webhook_info"

echo ""
echo "✅ Webhook setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Make sure your server is running on https://cpcheckbot.thaipay.info"
echo "2. Ensure the /telegram-webhook endpoint is accessible"
echo "3. Test by sending a message to your bot"
