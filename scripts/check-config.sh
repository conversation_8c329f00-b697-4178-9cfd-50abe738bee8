#!/bin/bash

# 配置检查脚本
# 用于验证部署配置是否正确

set -e

echo "🔍 配置检查脚本"
echo "================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check_env_var() {
    local var_name=$1
    local var_value=$2
    local is_required=${3:-true}
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -z "$var_value" ]; then
        if [ "$is_required" = true ]; then
            echo -e "${RED}❌ $var_name: 未设置（必需）${NC}"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        else
            echo -e "${YELLOW}⚠️  $var_name: 未设置（可选）${NC}"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        fi
    else
        echo -e "${GREEN}✅ $var_name: 已设置${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    fi
}

# 加载.env文件
if [ -f .env ]; then
    echo "📝 加载.env文件..."
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
    echo ""
else
    echo -e "${RED}❌ .env文件不存在${NC}"
    echo "请先复制.env.example为.env并配置相关参数"
    exit 1
fi

echo "🔧 检查基础配置..."
check_env_var "PORT" "$PORT" false
check_env_var "TELEGRAM_BOT_TOKEN" "$TELEGRAM_BOT_TOKEN" true

echo ""
echo "🌐 检查第三方API配置..."
check_env_var "THIRD_PARTY_API_BASE_URL" "$THIRD_PARTY_API_BASE_URL" true
check_env_var "THIRD_PARTY_API_QUERY_PATH" "$THIRD_PARTY_API_QUERY_PATH" true
check_env_var "THIRD_API_KEY" "$THIRD_API_KEY" true

echo ""
echo "📱 检查二维码API配置..."
check_env_var "QRCODE_API_ACCOUNT_NO" "$QRCODE_API_ACCOUNT_NO" true
check_env_var "QRCODE_API_BASE_URL" "$QRCODE_API_BASE_URL" true
check_env_var "QRCODE_API_LICENSE_KEY" "$QRCODE_API_LICENSE_KEY" true
check_env_var "QRCODE_API_ACCESS_KEY" "$QRCODE_API_ACCESS_KEY" true

echo ""
echo "👥 检查群组配置..."
check_env_var "CUSTOMER_SERVICE_GROUP" "$CUSTOMER_SERVICE_GROUP" true
check_env_var "SUPPLIER_GROUP_TOPPAY" "$SUPPLIER_GROUP_TOPPAY" true
check_env_var "SUPPLIER_GROUP_VADERPAY" "$SUPPLIER_GROUP_VADERPAY" true
check_env_var "SUPPLIER_GROUP_THPAY" "$SUPPLIER_GROUP_THPAY" true
check_env_var "SUPPLIER_GROUP_EXKUB" "$SUPPLIER_GROUP_EXKUB" true

echo ""
echo "📋 检查日志配置..."
check_env_var "LOG_LEVEL" "$LOG_LEVEL" false
check_env_var "LOG_FILE_PATH" "$LOG_FILE_PATH" false
check_env_var "LOG_MAX_SIZE" "$LOG_MAX_SIZE" false
check_env_var "LOG_MAX_BACKUPS" "$LOG_MAX_BACKUPS" false
check_env_var "LOG_MAX_AGE" "$LOG_MAX_AGE" false
check_env_var "LOG_COMPRESS" "$LOG_COMPRESS" false
check_env_var "LOG_CONSOLE" "$LOG_CONSOLE" false

echo ""
echo "📁 检查文件和目录..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 3))

if [ -f "main.go" ]; then
    echo -e "${GREEN}✅ main.go: 存在${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}❌ main.go: 不存在${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

if [ -f "docker-compose.yml" ]; then
    echo -e "${GREEN}✅ docker-compose.yml: 存在${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}❌ docker-compose.yml: 不存在${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

if [ -d "logs" ] || mkdir -p logs; then
    echo -e "${GREEN}✅ logs目录: 存在或已创建${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}❌ logs目录: 无法创建${NC}"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

echo ""
echo "🐳 检查Docker配置..."
if command -v docker &> /dev/null; then
    echo -e "${GREEN}✅ Docker: 已安装${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${YELLOW}⚠️  Docker: 未安装（如果使用Docker部署则需要）${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

if command -v docker-compose &> /dev/null; then
    echo -e "${GREEN}✅ Docker Compose: 已安装${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${YELLOW}⚠️  Docker Compose: 未安装（如果使用Docker部署则需要）${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
fi
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))

echo ""
echo "📊 检查结果汇总"
echo "================"
echo -e "总检查项: $TOTAL_CHECKS"
echo -e "${GREEN}通过: $PASSED_CHECKS${NC}"
echo -e "${RED}失败: $FAILED_CHECKS${NC}"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo ""
    echo -e "${GREEN}🎉 所有检查通过！配置看起来正确。${NC}"
    echo ""
    echo "下一步："
    echo "1. 运行 'make compose-up' 启动服务"
    echo "2. 或运行 'make run' 进行本地开发"
    echo "3. 使用 'make setup-webhook' 设置Telegram Webhook"
    exit 0
else
    echo ""
    echo -e "${RED}❌ 发现 $FAILED_CHECKS 个配置问题，请修复后重新检查。${NC}"
    exit 1
fi
