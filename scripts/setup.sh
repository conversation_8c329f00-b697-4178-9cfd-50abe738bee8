#!/bin/bash

# Telegram订单处理机器人设置脚本

set -e

echo "🤖 Telegram订单处理机器人设置脚本"
echo "=================================="

# 检查是否安装了必要的工具
check_dependencies() {
    echo "检查依赖..."
    
    if ! command -v go &> /dev/null; then
        echo "❌ Go未安装，请先安装Go 1.21或更高版本"
        exit 1
    fi
    
    if ! command -v docker &> /dev/null; then
        echo "⚠️  Docker未安装，将无法使用Docker部署"
    fi
    
    echo "✅ 依赖检查完成"
}

# 设置环境变量
setup_env() {
    echo "设置环境变量..."
    
    if [ ! -f .env ]; then
        cp .env.example .env
        echo "📝 已创建.env文件"
        echo "请编辑.env文件并填入以下必要配置："
        echo "  - TELEGRAM_BOT_TOKEN: 你的Telegram Bot Token"
        echo "  - THIRD_PARTY_API_BASE_URL: 第三方API基础URL"
        echo "  - THIRD_API_KEY: 第三方API密钥"
        echo "  - QRCODE_API_ACCOUNT_NO: 二维码API账户号"
        echo "  - QRCODE_API_BASE_URL: 二维码API基础URL"
        echo "  - QRCODE_API_LICENSE_KEY: 二维码API许可证密钥"
        echo "  - QRCODE_API_ACCESS_KEY: 二维码API访问密钥"
        echo "  - 各个群组ID配置"
        echo ""
        read -p "是否现在编辑.env文件? (y/n): " edit_env
        if [ "$edit_env" = "y" ]; then
            ${EDITOR:-nano} .env
        fi
    else
        echo "✅ .env文件已存在"
    fi
}

# 安装Go依赖
install_deps() {
    echo "安装Go依赖..."
    go mod tidy
    echo "✅ Go依赖安装完成"
}

# 运行测试
run_tests() {
    echo "运行测试..."
    go test -v ./...
    echo "✅ 测试通过"
}

# 构建应用
build_app() {
    echo "构建应用..."
    mkdir -p bin
    go build -o bin/telegram-order-bot .
    echo "✅ 应用构建完成"
}

# 设置Webhook
setup_webhook() {
    echo "设置Telegram Webhook..."
    
    # 从.env文件读取配置
    if [ -f .env ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    if [ -z "$TELEGRAM_BOT_TOKEN" ]; then
        echo "❌ 请在.env文件中设置TELEGRAM_BOT_TOKEN"
        return 1
    fi
    
    read -p "请输入你的Webhook URL (例如: https://yourdomain.com): " webhook_url
    
    if [ -z "$webhook_url" ]; then
        echo "❌ Webhook URL不能为空"
        return 1
    fi
    
    # 设置webhook
    response=$(curl -s -X POST "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/setWebhook" \
        -H "Content-Type: application/json" \
        -d "{\"url\": \"$webhook_url/webhook/$TELEGRAM_BOT_TOKEN\"}")
    
    if echo "$response" | grep -q '"ok":true'; then
        echo "✅ Webhook设置成功"
        echo "Webhook URL: $webhook_url/webhook/$TELEGRAM_BOT_TOKEN"
    else
        echo "❌ Webhook设置失败: $response"
        return 1
    fi
}

# 主菜单
main_menu() {
    echo ""
    echo "请选择操作："
    echo "1. 完整设置 (推荐新用户)"
    echo "2. 仅安装依赖"
    echo "3. 仅运行测试"
    echo "4. 仅构建应用"
    echo "5. 设置Webhook"
    echo "6. 启动应用"
    echo "7. 退出"
    echo ""
    read -p "请输入选项 (1-7): " choice
    
    case $choice in
        1)
            check_dependencies
            setup_env
            install_deps
            run_tests
            build_app
            echo ""
            echo "🎉 完整设置完成！"
            echo "下一步："
            echo "1. 编辑.env文件配置必要参数"
            echo "2. 运行 './scripts/setup.sh' 选择选项5设置Webhook"
            echo "3. 运行 'make run' 或 './bin/telegram-order-bot' 启动应用"
            ;;
        2)
            install_deps
            ;;
        3)
            run_tests
            ;;
        4)
            build_app
            ;;
        5)
            setup_webhook
            ;;
        6)
            echo "启动应用..."
            if [ -f bin/telegram-order-bot ]; then
                ./bin/telegram-order-bot
            else
                echo "❌ 应用未构建，请先运行构建"
                build_app
                ./bin/telegram-order-bot
            fi
            ;;
        7)
            echo "👋 再见！"
            exit 0
            ;;
        *)
            echo "❌ 无效选项"
            main_menu
            ;;
    esac
}

# 脚本入口
echo "当前目录: $(pwd)"
echo ""

# 检查是否在正确的目录
if [ ! -f "main.go" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

main_menu
