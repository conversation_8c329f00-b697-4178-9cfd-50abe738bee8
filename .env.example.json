# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************

# Third Party API Configuration
THIRD_PARTY_API_BASE_URL=https://api.example.com
THIRD_PARTY_API_QUERY_PATH=/query
THIRD_API_KEY=your_api_key

# QR Code API Configuration
QRCODE_API_BASE_URL=https://qrcode-api.example.com
QRCODE_API_ACCOUNT_NO=your_account
QRCODE_API_LICENSE_KEY=your_license_key
QRCODE_API_ACCESS_KEY=your_access_key

# Group Configuration - JSON Format (推荐)
SUPPLIER_GROUPS_JSON=[{"name":"TopPay Supplier Group","group_id":"-*************","payment_institution":"TAIP496-Cloud168-TOPPAY"},{"name":"VaderPay Supplier Group","group_id":"-*************","payment_institution":"VaderPay-superkapoo888"},{"name":"THPay Supplier Group","group_id":"-*************","payment_institution":"THPay-THPayCloudpay888"},{"name":"Exkub SCB Supplier Group","group_id":"-*************","payment_institution":"ExkubScbRs-exkubscbIOne"},{"name":"Exkub GSB Supplier Group","group_id":"-*************","payment_institution":"exkubgsb"}]

# 或者使用传统的环境变量方式（如果不设置SUPPLIER_GROUPS_JSON）
# SUPPLIER_GROUP_TOPPAY=-*************
# SUPPLIER_GROUP_VADERPAY=-*************
# SUPPLIER_GROUP_THPAY=-*************
# SUPPLIER_GROUP_EXKUB=-*************

# 商户群组
MERCHANT_GROUPS=-*************,-*************

# 客服群组
CUSTOMER_SERVICE_GROUP=-1001234567897

# Server Configuration
PORT=8080
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=100
LOG_MAX_BACKUPS=3
LOG_MAX_AGE=28
LOG_COMPRESS=true
LOG_CONSOLE=true

# Status Messages
ENABLE_STATUS_MESSAGES=false
