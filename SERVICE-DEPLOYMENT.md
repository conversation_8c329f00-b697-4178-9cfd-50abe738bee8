# Telegram Order Bot - Linux Service Deployment

This guide explains how to deploy the Telegram Order Bot as a Linux systemd service.

## Files Overview

- `telegram-order-bot.service` - Systemd service configuration
- `install-service.sh` - Installation script
- `manage-service.sh` - Service management script
- `telegram-order-bot-linux` - Linux binary executable

## Installation Steps

### 1. Prepare Files

Ensure you have the following files in your deployment directory:
```bash
telegram-order-bot-linux    # The compiled binary
telegram-order-bot.service  # Systemd service file
install-service.sh          # Installation script
manage-service.sh           # Management script
.env                        # Environment configuration
```

### 2. Configure Environment

Create or update your `.env` file with the required configuration:
```bash
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_bot_token_here

# Third Party API Configuration
THIRD_PARTY_API_BASE_URL=https://api.example.com
THIRD_PARTY_API_QUERY_PATH=/query
THIRD_API_KEY=your_api_key

# QR Code API Configuration
QRCODE_API_BASE_URL=https://qrcode-api.example.com
QRCODE_API_ACCOUNT_NO=your_account
QRCODE_API_LICENSE_KEY=your_license_key
QRCODE_API_ACCESS_KEY=your_access_key

# Group Configuration
MERCHANT_GROUPS=-*************,-*************
SUPPLIER_GROUP_EXAMPLE=-*************
CUSTOMER_SERVICE_GROUP=-*************

# Server Configuration
PORT=8080
LOG_LEVEL=info
```

### 3. Install Service

Run the installation script as root:
```bash
sudo ./install-service.sh
```

This script will:
- Create a system user `telegram-bot`
- Create installation directory `/opt/telegram-order-bot`
- Copy binary and configuration files
- Install systemd service
- Set proper permissions

### 4. Enable and Start Service

```bash
# Enable service to start on boot
sudo systemctl enable telegram-order-bot

# Start the service
sudo systemctl start telegram-order-bot

# Check service status
sudo systemctl status telegram-order-bot
```

## Service Management

Use the management script for common operations:

```bash
# Start service
./manage-service.sh start

# Stop service
./manage-service.sh stop

# Restart service
./manage-service.sh restart

# Check status
./manage-service.sh status

# Enable auto-start on boot
./manage-service.sh enable

# Disable auto-start on boot
./manage-service.sh disable

# View logs (real-time)
./manage-service.sh logs

# Update binary
./manage-service.sh update
```

## Service Configuration

The service is configured with the following settings:

- **User**: `telegram-bot` (system user)
- **Working Directory**: `/opt/telegram-order-bot`
- **Logs**: Available via `journalctl -u telegram-order-bot`
- **Auto-restart**: Enabled with 5-second delay
- **Security**: Enhanced security settings enabled

## Logs and Monitoring

### View Logs
```bash
# Real-time logs
journalctl -u telegram-order-bot -f

# Recent logs
journalctl -u telegram-order-bot -n 100

# Logs from specific time
journalctl -u telegram-order-bot --since "2024-01-01 00:00:00"
```

### Check Service Status
```bash
systemctl status telegram-order-bot
```

## Updating the Service

To update the bot with a new binary:

1. Place the new `telegram-order-bot-linux` binary in your deployment directory
2. Run the update command:
   ```bash
   ./manage-service.sh update
   ```

This will:
- Stop the service
- Backup the old binary
- Install the new binary
- Restart the service

## Troubleshooting

### Service Won't Start
1. Check logs: `journalctl -u telegram-order-bot -n 50`
2. Verify `.env` file configuration
3. Check file permissions: `ls -la /opt/telegram-order-bot/`
4. Ensure binary is executable: `chmod +x /opt/telegram-order-bot/telegram-order-bot-linux`

### Permission Issues
```bash
# Fix ownership
sudo chown -R telegram-bot:telegram-bot /opt/telegram-order-bot

# Fix permissions
sudo chmod +x /opt/telegram-order-bot/telegram-order-bot-linux
sudo chmod 600 /opt/telegram-order-bot/.env
```

### Configuration Issues
- Verify all required environment variables are set in `.env`
- Check group IDs are correct (use `get-group-id.sh` to find them)
- Ensure API endpoints are accessible

## Uninstalling

To remove the service:
```bash
# Stop and disable service
sudo systemctl stop telegram-order-bot
sudo systemctl disable telegram-order-bot

# Remove service file
sudo rm /etc/systemd/system/telegram-order-bot.service

# Reload systemd
sudo systemctl daemon-reload

# Remove installation directory (optional)
sudo rm -rf /opt/telegram-order-bot

# Remove user (optional)
sudo userdel telegram-bot
```

## Security Notes

- The service runs as a non-privileged user (`telegram-bot`)
- Enhanced security settings are enabled in the service file
- Environment file permissions are restricted (600)
- The service has limited access to the filesystem

For additional security, consider:
- Using a firewall to restrict network access
- Regularly updating the system and dependencies
- Monitoring logs for suspicious activity
- Using strong API keys and tokens
