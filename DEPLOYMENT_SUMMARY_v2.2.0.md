# Telegram Order Bot v2.2.0 - Linux部署包摘要

## 📦 编译完成

### 包文件信息
- **文件名**: `telegram-order-bot-v2.2.0-linux-multiarch.tar.gz`
- **大小**: 14.5 MB
- **版本**: v2.2.0
- **编译时间**: 2025-07-17 01:20
- **目标平台**: Linux (多架构支持)

### 🎉 重大新功能

#### 📸 Media Group多图相册支持
- ✅ **Telegram相册处理**: 完整支持Telegram的Media Group机制
- ✅ **智能消息收集**: 自动收集同一相册中的所有消息
- ✅ **延迟处理机制**: 2秒延迟确保收集完整相册
- ✅ **多图二维码扫描**: 逐个扫描每张图片的二维码
- ✅ **精确金额累加**: 使用decimal类型累加所有图片金额
- ✅ **三种金额比较**: 精确匹配、向上取整、向下取整

#### 🏷️ Telegram API群组名称获取
- ✅ **真实群组名称**: 通过Telegram `getChat` API获取群组真实名称
- ✅ **智能缓存机制**: 避免重复API调用，提高性能
- ✅ **完善错误处理**: 处理权限不足、群组不存在等异常情况
- ✅ **回退机制**: API失败时自动使用配置文件名称
- ✅ **增强转发格式**: 转发消息包含真实群组名称和时间戳

### 📁 包内容

```
telegram-order-bot-v2.2.0-linux-package/
├── telegram-order-bot-linux-amd64    # x86_64架构二进制文件 (14.1MB)
├── telegram-order-bot-linux-arm64    # aarch64架构二进制文件 (13.4MB)
├── start.sh                          # 智能启动脚本 v2.2.0 ⭐
├── install.sh                        # 多架构安装脚本 v2.2.0 ⭐
├── .env.example                      # 配置文件模板
├── get-group-id.sh                   # 群组ID获取工具
├── README.md                         # 项目说明 (更新)
├── DEPLOYMENT.md                     # 部署文档
└── VERSION.md                        # 版本信息 v2.2.0 ⭐
```

### 🚀 快速部署

#### 自动安装 (推荐)
```bash
# 解压部署包
tar -xzf telegram-order-bot-v2.2.0-linux-multiarch.tar.gz
cd telegram-order-bot-v2.2.0-linux-package

# 自动安装 (自动检测架构)
sudo ./install.sh

# 编辑配置文件
sudo nano /opt/telegram-order-bot/.env

# 启动服务
sudo systemctl start telegram-order-bot
sudo systemctl status telegram-order-bot
```

#### 手动启动
```bash
# 解压部署包
tar -xzf telegram-order-bot-v2.2.0-linux-multiarch.tar.gz
cd telegram-order-bot-v2.2.0-linux-package

# 复制配置文件
cp .env.example .env
# 编辑配置文件
nano .env

# 启动程序 (自动检测架构)
./start.sh
```

### 🔧 支持的架构
- ✅ **x86_64 (amd64)**: Intel/AMD 64位处理器
- ✅ **aarch64 (arm64)**: ARM 64位处理器 (树莓派4、Apple M1等)
- ❌ **armv7l/armv6l**: 32位ARM (暂不支持)

### 📋 支持的订单格式

#### 传统格式（保持不变）
1. **Order Number: 订单号** (图片+文本)
2. **Merchant Order No. 订单号** (图片+文本)
3. **Merchant: 订单号** (图片+文本)
4. **订单号** (图片+单行文本)

#### 新增格式 ⭐
5. **Media Group相册** (多图片+单行订单号)
   - 支持2张或更多图片的相册消息
   - Caption（订单号）只在最后一条消息中
   - 自动获取每张图片的最大尺寸版本
   - 扫描所有图片二维码并累加金额

### 🎯 转发消息格式升级

#### 新的转发格式
```
📍 来源: 商户群: ABC支付收款群          # 真实群组名称
🕐 时间: 2024-07-17 01:15:30           # 消息时间戳
📋 订单ID: ORDER123456                 # 提交订单ID
📸 多图相册: AgACAgIAAxkBAAIC          # Media Group ID (仅多图)
━━━━━━━━━━━━━━━━━━━━
原始消息内容...

❌ 处理失败: 具体失败原因
💡 提示: 这是多图相册中的一张图片 (仅多图)
```

### 💰 金额处理优势

#### 使用Decimal类型
- **精确计算**: 避免浮点数精度问题
- **金融级精度**: 适合处理货币金额
- **多图片累加**: 精确计算多张图片的金额总和

#### 三种比较方式
- 精确匹配
- 向上取整匹配 (ceil)
- 向下取整匹配 (floor)

### 🔒 安全特性

- 专用用户运行 (telegram-bot)
- 适当的文件权限设置
- systemd 安全配置
- 详细的错误处理和日志记录
- 静态编译，无外部依赖

### 📊 技术规格

- **Go版本**: 1.20+
- **架构**: Linux amd64/arm64
- **编译方式**: 静态编译 (CGO_ENABLED=0)
- **依赖**: github.com/shopspring/decimal
- **服务管理**: systemd
- **日志**: 文件日志 + systemd journal

### 🧪 测试覆盖

- ✅ Media Group消息识别测试
- ✅ 多图片订单解析测试
- ✅ Decimal金额累加测试
- ✅ Telegram API群组名称获取测试
- ✅ 错误处理和回退机制测试
- ✅ 缓存机制测试

### 📝 文档完整性

- ✅ 部署指南 (DEPLOYMENT.md)
- ✅ 版本信息 (VERSION.md) - 更新至v2.2.0
- ✅ 项目说明 (README.md) - 包含新功能说明
- ✅ 配置模板 (.env.example)
- ✅ 启动脚本 (start.sh) - v2.2.0
- ✅ 安装脚本 (install.sh) - v2.2.0

### 🔄 兼容性

- ✅ 完全向后兼容现有4种消息格式
- ✅ 不影响现有配置和API接口
- ✅ 平滑升级，无需额外配置
- ✅ 支持从v2.1.x平滑升级

### 📈 性能优化

- 并发处理多个订单
- 高效的图片下载和处理
- 优化的二维码扫描
- 智能的错误恢复机制
- 群组名称缓存机制

### 🎉 典型使用场景

#### 场景1: 多图相册订单
1. 商户选择3张付款截图创建相册
2. 在最后一张图片添加订单号作为caption
3. 发送到商户群
4. 机器人自动识别为Media Group订单
5. 逐个扫描3张图片的二维码
6. 累加3张图片的金额
7. 与订单查询金额进行比较验证

#### 场景2: 群组名称显示
1. 群组改名为"ABC公司正式收款群"
2. 转发消息自动显示真实群组名称
3. 客服立即识别消息来源，提高处理效率

---

## 🎉 部署包已准备就绪！

**文件**: `telegram-order-bot-v2.2.0-linux-multiarch.tar.gz`

这个部署包包含了完整的Media Group多图相册支持和Telegram API群组名称获取功能，使用decimal类型进行精确金额计算，支持多架构部署，并提供了完善的安装和部署工具。

### 🚀 立即开始使用：
1. 下载部署包: `telegram-order-bot-v2.2.0-linux-multiarch.tar.gz`
2. 解压并运行 `sudo ./install.sh` (自动检测架构)
3. 配置 `.env` 文件
4. 启动服务并享受新功能！

### 🎯 升级亮点：
- 🆕 **Media Group支持**: 处理多图相册订单
- 🏷️ **真实群组名称**: 显示Telegram群组真实名称
- 💰 **精确金额计算**: Decimal类型金融级精度
- 🔧 **智能错误处理**: 完善的异常处理机制
