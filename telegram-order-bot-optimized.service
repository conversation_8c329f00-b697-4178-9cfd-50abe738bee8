[Unit]
Description=Telegram Order Bot Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=telegram-bot
Group=telegram-bot
WorkingDirectory=/www/robot
ExecStart=/www/robot/telegram-order-bot-linux
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5

# 日志配置优化
# 方案1: 只使用文件日志，避免与systemd journal冲突
StandardOutput=null
StandardError=journal
SyslogIdentifier=telegram-order-bot

# 环境配置优化
# 使用 - 前缀表示文件可选，避免启动失败
EnvironmentFile=-/www/robot/.env
# 强制关闭控制台日志，避免重复
Environment="LOG_CONSOLE=false"
Environment="LOG_FILE_PATH=/www/robot/logs/app.log"
Environment="LOG_LEVEL=info"

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/www/robot/logs

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
