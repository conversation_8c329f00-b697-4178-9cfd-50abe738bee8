#!/bin/bash

echo "=== Telegram Order Bot Deployment Test ==="

# Check if binary exists
echo "1. Checking binary file..."
if [ -f "/www/robot/telegram-order-bot-linux" ]; then
    echo "✅ Binary exists"
    ls -la /www/robot/telegram-order-bot-linux
else
    echo "❌ Binary not found"
    exit 1
fi

# Check if user exists
echo "2. Checking user..."
if id telegram-bot &>/dev/null; then
    echo "✅ User telegram-bot exists"
    id telegram-bot
else
    echo "❌ User telegram-bot not found"
    echo "Creating user..."
    sudo groupadd --system telegram-bot
    sudo useradd --system --gid telegram-bot --home-dir /www/robot --shell /bin/false telegram-bot
fi

# Check permissions
echo "3. Checking permissions..."
sudo chown -R telegram-bot:telegram-bot /www/robot
sudo chmod +x /www/robot/telegram-order-bot-linux

# Check .env file
echo "4. Checking .env file..."
if [ -f "/www/robot/.env" ]; then
    echo "✅ .env file exists"
    sudo chmod 600 /www/robot/.env
    sudo chown telegram-bot:telegram-bot /www/robot/.env
else
    echo "❌ .env file not found"
fi

# Test binary execution
echo "5. Testing binary execution..."
sudo -u telegram-bot /www/robot/telegram-order-bot-linux --version 2>/dev/null || echo "Binary test completed (may show error if no --version flag)"

# Check service file
echo "6. Checking service file..."
if [ -f "/etc/systemd/system/telegram-order-bot.service" ]; then
    echo "✅ Service file exists"
else
    echo "❌ Service file not found"
fi

echo "=== Test completed ==="
