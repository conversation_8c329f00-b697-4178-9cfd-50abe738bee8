#!/bin/bash

# Telegram Order Bot Service Installation Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_NAME="telegram-order-bot"
SERVICE_USER="telegram-bot"
SERVICE_GROUP="telegram-bot"
INSTALL_DIR="/opt/telegram-order-bot"
BINARY_NAME="telegram-order-bot-linux"

echo -e "${BLUE}🤖 Telegram Order Bot Service Installation${NC}"
echo "=========================================="

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo -e "${RED}❌ This script must be run as root${NC}"
   exit 1
fi

# Check if binary exists
if [[ ! -f "./${BINARY_NAME}" ]]; then
    echo -e "${RED}❌ Binary file ${BINARY_NAME} not found in current directory${NC}"
    exit 1
fi

# Check if .env file exists
if [[ ! -f "./.env" ]]; then
    echo -e "${YELLOW}⚠️  .env file not found. Please create it before starting the service.${NC}"
fi

echo -e "${BLUE}📋 Creating system user and group...${NC}"
# Create system user and group
if ! getent group ${SERVICE_GROUP} > /dev/null 2>&1; then
    groupadd --system ${SERVICE_GROUP}
    echo -e "${GREEN}✅ Created group: ${SERVICE_GROUP}${NC}"
else
    echo -e "${YELLOW}⚠️  Group ${SERVICE_GROUP} already exists${NC}"
fi

if ! getent passwd ${SERVICE_USER} > /dev/null 2>&1; then
    useradd --system --gid ${SERVICE_GROUP} --create-home --home-dir ${INSTALL_DIR} --shell /bin/false ${SERVICE_USER}
    echo -e "${GREEN}✅ Created user: ${SERVICE_USER}${NC}"
else
    echo -e "${YELLOW}⚠️  User ${SERVICE_USER} already exists${NC}"
fi

echo -e "${BLUE}📁 Setting up installation directory...${NC}"
# Create installation directory
mkdir -p ${INSTALL_DIR}
mkdir -p ${INSTALL_DIR}/logs

# Copy files
cp ./${BINARY_NAME} ${INSTALL_DIR}/
chmod +x ${INSTALL_DIR}/${BINARY_NAME}

if [[ -f "./.env" ]]; then
    cp ./.env ${INSTALL_DIR}/
    chmod 600 ${INSTALL_DIR}/.env
    echo -e "${GREEN}✅ Copied .env file${NC}"
fi

# Set ownership
chown -R ${SERVICE_USER}:${SERVICE_GROUP} ${INSTALL_DIR}
echo -e "${GREEN}✅ Set ownership to ${SERVICE_USER}:${SERVICE_GROUP}${NC}"

echo -e "${BLUE}🔧 Installing systemd service...${NC}"
# Install systemd service
cp ./telegram-order-bot.service /etc/systemd/system/
systemctl daemon-reload
echo -e "${GREEN}✅ Service installed${NC}"

echo -e "${BLUE}🚀 Service installation completed!${NC}"
echo ""
echo -e "${YELLOW}📋 Next steps:${NC}"
echo "1. Configure your .env file: ${INSTALL_DIR}/.env"
echo "2. Enable the service: systemctl enable ${SERVICE_NAME}"
echo "3. Start the service: systemctl start ${SERVICE_NAME}"
echo "4. Check status: systemctl status ${SERVICE_NAME}"
echo "5. View logs: journalctl -u ${SERVICE_NAME} -f"
echo ""
echo -e "${GREEN}🎉 Installation completed successfully!${NC}"
