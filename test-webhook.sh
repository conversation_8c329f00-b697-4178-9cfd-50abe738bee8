#!/bin/bash

# Webhook测试脚本

BOT_TOKEN="8074006804:AAGI8hqRprFBAWlKsPmT9qTI63M3OvjmI_E"
WEBHOOK_URL="https://check.tgbot.rushingpay.com/webhook/${BOT_TOKEN}"

echo "🧪 Webhook测试工具"
echo "=================="
echo "Webhook URL: ${WEBHOOK_URL}"
echo ""

# 测试多个Webhook端点
echo "1. 测试Webhook端点可访问性..."

# 测试数据
test_data='{"update_id": 1, "message": {"message_id": 1, "date": 1234567890, "chat": {"id": 1, "type": "private"}, "from": {"id": 1, "is_bot": false, "first_name": "Test"}, "text": "test"}}'

# 测试路径1: 完整Token路径
echo "   测试路径1: ${WEBHOOK_URL}"
response1=$(curl -s -o /dev/null -w "%{http_code}" -X POST "${WEBHOOK_URL}" \
    -H "Content-Type: application/json" \
    -d "$test_data")

if [ "$response1" = "200" ]; then
    echo "   ✅ 完整Token路径可访问 (HTTP $response1)"
else
    echo "   ❌ 完整Token路径不可访问 (HTTP $response1)"
fi

# 测试路径2: 通用路径
webhook_generic="https://check.tgbot.rushingpay.com/webhook"
echo "   测试路径2: ${webhook_generic}"
response2=$(curl -s -o /dev/null -w "%{http_code}" -X POST "${webhook_generic}" \
    -H "Content-Type: application/json" \
    -d "$test_data")

if [ "$response2" = "200" ]; then
    echo "   ✅ 通用路径可访问 (HTTP $response2)"
else
    echo "   ❌ 通用路径不可访问 (HTTP $response2)"
fi

# 总结
if [ "$response1" = "200" ] || [ "$response2" = "200" ]; then
    echo "   🎉 至少有一个Webhook路径可用！"
else
    echo "   ⚠️  所有Webhook路径都不可访问"
fi

echo ""

# 检查Webhook状态
echo "2. 检查Telegram Webhook状态..."
webhook_info=$(curl -s -X GET "https://api.telegram.org/bot${BOT_TOKEN}/getWebhookInfo")

if echo "$webhook_info" | grep -q '"ok":true'; then
    echo "✅ Webhook状态查询成功"
    
    # 提取信息
    url=$(echo "$webhook_info" | grep -o '"url":"[^"]*"' | cut -d'"' -f4)
    pending_count=$(echo "$webhook_info" | grep -o '"pending_update_count":[0-9]*' | cut -d':' -f2)
    last_error_date=$(echo "$webhook_info" | grep -o '"last_error_date":[0-9]*' | cut -d':' -f2)
    
    echo "   当前URL: $url"
    echo "   待处理消息: $pending_count"
    
    if [ -n "$last_error_date" ] && [ "$last_error_date" != "null" ]; then
        echo "   ⚠️  最后错误时间: $(date -d @$last_error_date 2>/dev/null || echo $last_error_date)"
    else
        echo "   ✅ 无错误记录"
    fi
else
    echo "❌ Webhook状态查询失败"
fi

echo ""

# 测试Bot基本功能
echo "3. 测试Bot基本功能..."
bot_info=$(curl -s -X GET "https://api.telegram.org/bot${BOT_TOKEN}/getMe")

if echo "$bot_info" | grep -q '"ok":true'; then
    echo "✅ Bot连接正常"
    
    username=$(echo "$bot_info" | grep -o '"username":"[^"]*"' | cut -d'"' -f4)
    first_name=$(echo "$bot_info" | grep -o '"first_name":"[^"]*"' | cut -d'"' -f4)
    
    echo "   Bot用户名: @$username"
    echo "   Bot名称: $first_name"
else
    echo "❌ Bot连接失败"
fi

echo ""
echo "🏁 测试完成！"
echo ""
echo "💡 提示："
echo "   - 如果Webhook端点不可访问，请检查服务器是否正在运行"
echo "   - 如果有待处理消息，说明Webhook可能有问题"
echo "   - 确保域名SSL证书有效"
