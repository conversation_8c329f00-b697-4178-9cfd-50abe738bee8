#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 检查项目文件结构...\n');

const requiredFiles = [
  'package.json',
  'vite.config.ts',
  'tailwind.config.js',
  'tsconfig.json',
  'index.html',
  'src/main.ts',
  'src/App.vue',
  'src/style.css',
  'src/types/group.ts',
  'src/services/api.ts',
  'src/services/groupService.ts',
  'src/components/ui/button.vue',
  'src/components/ui/input.vue',
  'src/components/ui/dialog.vue',
  'src/components/ui/alert-dialog.vue',
  'src/components/GroupFormDialog.vue',
  'src/components/DeleteConfirmDialog.vue',
  'src/views/GroupManagement.vue'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件不存在`);
    allFilesExist = false;
  }
});

console.log('\n📦 检查 package.json 依赖...\n');

try {
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, 'package.json'), 'utf8'));
  
  const requiredDeps = [
    'vue',
    'vue-router',
    'pinia',
    'axios',
    '@vueuse/core',
    'lucide-vue-next',
    'class-variance-authority',
    'clsx',
    'tailwind-merge'
  ];

  const requiredDevDeps = [
    '@vitejs/plugin-vue',
    'typescript',
    'vite',
    'tailwindcss',
    'autoprefixer',
    'postcss'
  ];

  requiredDeps.forEach(dep => {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.dependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - 依赖缺失`);
      allFilesExist = false;
    }
  });

  console.log('\n开发依赖:');
  requiredDevDeps.forEach(dep => {
    if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      console.log(`✅ ${dep}: ${packageJson.devDependencies[dep]}`);
    } else {
      console.log(`❌ ${dep} - 开发依赖缺失`);
      allFilesExist = false;
    }
  });

} catch (error) {
  console.log('❌ 无法读取 package.json');
  allFilesExist = false;
}

console.log('\n' + '='.repeat(50));

if (allFilesExist) {
  console.log('🎉 项目结构检查通过！');
  console.log('\n📋 下一步操作:');
  console.log('1. 运行 npm install 安装依赖');
  console.log('2. 运行 npm run dev 启动开发服务器');
  console.log('3. 访问 http://localhost:3000');
} else {
  console.log('❌ 项目结构检查失败，请检查缺失的文件');
  process.exit(1);
}

console.log('\n🔗 确保后端服务运行在 http://localhost:8080');
console.log('📚 查看 README.md 获取详细说明');
