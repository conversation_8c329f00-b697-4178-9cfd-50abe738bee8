#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔧 测试配置文件...\n');

// 测试 PostCSS 配置
try {
  const postcssConfig = require('./postcss.config.js');
  if (postcssConfig && postcssConfig.plugins) {
    console.log('✅ PostCSS 配置正常');
  } else {
    console.log('❌ PostCSS 配置异常');
  }
} catch (error) {
  console.log('❌ PostCSS 配置加载失败:', error.message);
}

// 测试 Tailwind 配置
try {
  const tailwindConfig = require('./tailwind.config.js');
  if (tailwindConfig && tailwindConfig.content) {
    console.log('✅ Tailwind 配置正常');
  } else {
    console.log('❌ Tailwind 配置异常');
  }
} catch (error) {
  console.log('❌ Tailwind 配置加载失败:', error.message);
}

// 检查 package.json
try {
  const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
  if (packageJson.type === 'module') {
    console.log('⚠️  package.json 仍然包含 "type": "module"');
  } else {
    console.log('✅ package.json 配置正常');
  }
} catch (error) {
  console.log('❌ package.json 读取失败:', error.message);
}

console.log('\n🎯 配置检查完成！');
console.log('现在可以运行: npm run dev');
