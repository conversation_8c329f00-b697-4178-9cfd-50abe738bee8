# 部署指南

## 开发环境部署

### 前置要求
- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0
- 后端服务运行在 `http://localhost:8080`

### 快速开始

#### 方法1: 使用安装脚本
```bash
# Linux/macOS
chmod +x setup.sh
./setup.sh

# Windows
setup.bat
```

#### 方法2: 手动安装
```bash
# 1. 安装依赖
npm install

# 2. 启动开发服务器
npm run dev
```

访问 `http://localhost:3000` 查看应用。

## 生产环境部署

### 1. 构建生产版本
```bash
npm run build
```

构建产物将生成在 `dist/` 目录中。

### 2. 预览生产版本
```bash
npm run preview
```

### 3. 部署到Web服务器

#### Nginx 配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/frontend/dist;
    index index.html;

    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理到后端
    location /api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

#### Apache 配置示例
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/frontend/dist
    
    # 处理Vue Router的history模式
    <Directory "/path/to/frontend/dist">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>
    
    # API代理到后端
    ProxyPass /api/ http://localhost:8080/api/
    ProxyPassReverse /api/ http://localhost:8080/api/
</VirtualHost>
```

### 4. Docker 部署

#### Dockerfile
```dockerfile
# 构建阶段
FROM node:18-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### docker-compose.yml
```yaml
version: '3.8'
services:
  frontend:
    build: .
    ports:
      - "3000:80"
    depends_on:
      - backend
    environment:
      - NODE_ENV=production

  backend:
    # 后端服务配置
    ports:
      - "8080:8080"
```

## 环境变量配置

创建 `.env` 文件（可选）：
```env
# API基础URL（如果需要自定义）
VITE_API_BASE_URL=http://localhost:8080/api/v1

# 应用标题
VITE_APP_TITLE=群组管理系统
```

## 性能优化

### 1. 代码分割
项目已配置自动代码分割，路由组件会被自动分割。

### 2. 静态资源优化
- 图片压缩
- CSS/JS压缩
- Gzip压缩

### 3. CDN配置
可以将静态资源部署到CDN以提高加载速度。

## 监控和日志

### 1. 错误监控
建议集成错误监控服务（如Sentry）：

```typescript
// main.ts
import * as Sentry from "@sentry/vue";

Sentry.init({
  app,
  dsn: "YOUR_SENTRY_DSN",
});
```

### 2. 性能监控
使用Web Vitals监控页面性能：

```typescript
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);
```

## 故障排除

### 常见问题

1. **API请求失败**
   - 检查后端服务是否运行
   - 检查API代理配置
   - 检查CORS设置

2. **路由404错误**
   - 确保Web服务器配置了history模式支持
   - 检查base路径配置

3. **样式不生效**
   - 检查Tailwind CSS配置
   - 确保CSS文件正确导入

4. **构建失败**
   - 检查Node.js版本
   - 清除node_modules重新安装
   - 检查TypeScript类型错误

### 调试模式
```bash
# 启用详细日志
DEBUG=* npm run dev

# 类型检查
npm run type-check

# 代码检查
npm run lint
```

## 更新和维护

### 依赖更新
```bash
# 检查过期依赖
npm outdated

# 更新依赖
npm update

# 安全审计
npm audit
npm audit fix
```

### 版本发布
1. 更新版本号
2. 构建生产版本
3. 运行测试
4. 部署到生产环境
5. 创建Git标签
