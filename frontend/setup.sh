#!/bin/bash

echo "🚀 群组管理前端系统安装脚本"
echo "================================"

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js (版本 >= 16)"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"
echo "✅ npm 版本: $(npm --version)"
echo ""

# 检查项目结构
echo "🔍 检查项目结构..."
if command -v node &> /dev/null; then
    node test-install.js
    if [ $? -ne 0 ]; then
        echo "❌ 项目结构检查失败"
        exit 1
    fi
fi

# 安装依赖
echo "📦 安装项目依赖..."
npm install

if [ $? -eq 0 ]; then
    echo "✅ 依赖安装成功！"
else
    echo "❌ 依赖安装失败，请检查网络连接或尝试使用 yarn"
    exit 1
fi

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 可用命令："
echo "  npm run dev     - 启动开发服务器"
echo "  npm run build   - 构建生产版本"
echo "  npm run preview - 预览生产版本"
echo ""
echo "🌐 开发服务器将在 http://localhost:3000 启动"
echo "🔗 确保后端服务运行在 http://localhost:8080"
echo ""
echo "现在可以运行: npm run dev"
