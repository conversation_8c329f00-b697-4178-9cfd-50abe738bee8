#!/bin/bash

# Telegram Bot Webhook Setup Script

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WEBHOOK_URL="https://cpcheckbot.thaipay.info/telegram-webhook"

echo -e "${BLUE}🤖 Telegram Bot Webhook Setup${NC}"
echo "================================"

# Check if .env file exists
if [[ ! -f ".env" ]]; then
    echo -e "${RED}❌ .env file not found${NC}"
    echo "Please create .env file with TELEGRAM_BOT_TOKEN"
    exit 1
fi

# Load bot token from .env file
source .env

if [[ -z "$TELEGRAM_BOT_TOKEN" ]]; then
    echo -e "${RED}❌ TELEGRAM_BOT_TOKEN not found in .env file${NC}"
    exit 1
fi

echo -e "${BLUE}📡 Setting webhook URL: ${WEBHOOK_URL}${NC}"

# Set webhook
response=$(curl -s -X POST "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/setWebhook" \
     -H "Content-Type: application/json" \
     -d "{\"url\": \"${WEBHOOK_URL}\"}")

# Parse response
if echo "$response" | grep -q '"ok":true'; then
    echo -e "${GREEN}✅ Webhook set successfully!${NC}"
    echo -e "${GREEN}📍 Webhook URL: ${WEBHOOK_URL}${NC}"
else
    echo -e "${RED}❌ Failed to set webhook${NC}"
    echo -e "${RED}Response: $response${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}🔍 Verifying webhook info...${NC}"

# Get webhook info
info_response=$(curl -s "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/getWebhookInfo")

echo "$info_response" | python3 -c "
import json
import sys

try:
    data = json.load(sys.stdin)
    if data['ok']:
        webhook_info = data['result']
        print(f'📍 URL: {webhook_info.get(\"url\", \"Not set\")}')
        print(f'🔗 Has custom certificate: {webhook_info.get(\"has_custom_certificate\", False)}')
        print(f'📊 Pending update count: {webhook_info.get(\"pending_update_count\", 0)}')
        
        if webhook_info.get('last_error_date'):
            print(f'❌ Last error: {webhook_info.get(\"last_error_message\", \"Unknown error\")}')
        else:
            print('✅ No recent errors')
    else:
        print('❌ Failed to get webhook info')
        print(f'Error: {data.get(\"description\", \"Unknown error\")}')
except Exception as e:
    print(f'❌ Error parsing response: {e}')
"

echo ""
echo -e "${YELLOW}📋 Next steps:${NC}"
echo "1. Make sure your server is running on https://cpcheckbot.thaipay.info"
echo "2. Ensure the /telegram-webhook endpoint is accessible"
echo "3. Check server logs for incoming webhook requests"
echo "4. Test by sending a message to your bot"

echo ""
echo -e "${GREEN}🎉 Webhook setup completed!${NC}"
