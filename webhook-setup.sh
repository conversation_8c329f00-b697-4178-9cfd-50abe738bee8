#!/bin/bash

# Telegram Webhook 配置脚本
# 用于设置和管理Telegram Bot的Webhook

BOT_TOKEN="**********************************************"
DOMAIN="https://check.tgbot.rushingpay.com"
WEBHOOK_URL="${DOMAIN}/telegram-webhook"

echo "🤖 Telegram Bot Webhook 管理工具"
echo "================================"
echo "Bot Token: ${BOT_TOKEN}"
echo "Domain: ${DOMAIN}"
echo "Webhook URL: ${WEBHOOK_URL}"
echo ""

# 显示菜单
show_menu() {
    echo "请选择操作："
    echo "1. 设置Webhook"
    echo "2. 查看Webhook状态"
    echo "3. 删除Webhook"
    echo "4. 测试Bot连接"
    echo "5. 退出"
    echo ""
}

# 设置Webhook
set_webhook() {
    echo "🔧 正在设置Webhook..."
    response=$(curl -s -X POST "https://api.telegram.org/bot${BOT_TOKEN}/setWebhook" \
        -H "Content-Type: application/json" \
        -d "{\"url\": \"${WEBHOOK_URL}\"}")
    
    echo "响应: $response"
    
    if echo "$response" | grep -q '"ok":true'; then
        echo "✅ Webhook设置成功！"
    else
        echo "❌ Webhook设置失败！"
    fi
    echo ""
}

# 查看Webhook状态
get_webhook_info() {
    echo "📊 正在查询Webhook状态..."
    response=$(curl -s -X GET "https://api.telegram.org/bot${BOT_TOKEN}/getWebhookInfo")
    
    echo "响应: $response"
    
    # 解析响应
    if echo "$response" | grep -q '"ok":true'; then
        echo "✅ 查询成功！"
        
        # 提取关键信息
        url=$(echo "$response" | grep -o '"url":"[^"]*"' | cut -d'"' -f4)
        pending_count=$(echo "$response" | grep -o '"pending_update_count":[0-9]*' | cut -d':' -f2)
        
        echo "当前Webhook URL: $url"
        echo "待处理消息数: $pending_count"
    else
        echo "❌ 查询失败！"
    fi
    echo ""
}

# 删除Webhook
delete_webhook() {
    echo "🗑️  正在删除Webhook..."
    response=$(curl -s -X POST "https://api.telegram.org/bot${BOT_TOKEN}/deleteWebhook")
    
    echo "响应: $response"
    
    if echo "$response" | grep -q '"ok":true'; then
        echo "✅ Webhook删除成功！"
    else
        echo "❌ Webhook删除失败！"
    fi
    echo ""
}

# 测试Bot连接
test_bot() {
    echo "🔍 正在测试Bot连接..."
    response=$(curl -s -X GET "https://api.telegram.org/bot${BOT_TOKEN}/getMe")
    
    echo "响应: $response"
    
    if echo "$response" | grep -q '"ok":true'; then
        echo "✅ Bot连接正常！"
        
        # 提取Bot信息
        username=$(echo "$response" | grep -o '"username":"[^"]*"' | cut -d'"' -f4)
        first_name=$(echo "$response" | grep -o '"first_name":"[^"]*"' | cut -d'"' -f4)
        
        echo "Bot用户名: @$username"
        echo "Bot名称: $first_name"
    else
        echo "❌ Bot连接失败！请检查Token是否正确"
    fi
    echo ""
}

# 主循环
while true; do
    show_menu
    read -p "请输入选项 (1-5): " choice
    
    case $choice in
        1)
            set_webhook
            ;;
        2)
            get_webhook_info
            ;;
        3)
            delete_webhook
            ;;
        4)
            test_bot
            ;;
        5)
            echo "👋 再见！"
            exit 0
            ;;
        *)
            echo "❌ 无效选项，请重新选择"
            echo ""
            ;;
    esac
done
